unit ThreadPdCal;

interface

uses
  Windows, SysUtils, Classes, Forms, Winapi.Messages, Vcl.Dialogs, System.Math,
  RzCommon, CcMaterial, CommonUtil, DMUtil, System.Variants, XxDd;

type
  { 优化辅助函数类 - 提供高效的字符串转换和数组操作 }
  TOptimizationHelper = class
  private
    class var FStringCache: TStringList;
    class constructor Create;
    class destructor Destroy;
  public
    class function GetCachedIntValue(const AStr: string): Integer;
    class function StringListToIntArray(StringList: TStringList; StartIndex: Integer = 0; Count: Integer = -1): TArray<Integer>;
    class function CalculateStringListSum(StringList: TStringList; StartIndex: Integer = 0; Count: Integer = -1): Integer;
    class procedure ClearCache;
  end;

  TThreadPdCal = class
  private
  public
    constructor Create;
    destructor Destroy; override;
    procedure InitDetail(MaterialidStringList: TStringList; Fztypenum: Integer;
      kstypesecid: Integer; aDMUtilSave, aDMUtilSelect: TDataModule1);
    procedure FormatStrings();
    procedure Px(js, cs, fz_b, fz_e: string; Data1StringList, Data2StringList,
      Data3StringList: TStringList; DataStringList_Kc: TStringList;
      DataStringList_All: TStringList);
    procedure TestOptimizationPerformance;
    procedure FillGridEmptyNoFz();
    procedure FillGridEmpty();
    function CalOnceSingle(changei: Integer; SIn: array of Integer;
      FlagType: Integer): TList;
    function CalOnceFiveSingle(changei: Integer; SIn: array of Integer;
      FlagType: Integer): TList;
    function CalOnceTenSingle(changei: Integer; SIn: array of Integer): TList;
    procedure FillOnce(ListIn: TList);
    procedure FillOnceFive(ListIn: TList);
    procedure FillOnceTen(ListIn: TList);
    function FillOnceReturn(ListIn: TList; FlagType: Integer): TList;
    function FillOnceFiveReturn(ListIn: TList; FlagType: Integer): TList;
    function getNumberForArrayMax(SUsed: array of String): Integer;
    function getIndexForArrayMin(S: Array of Integer;
      SUsed: array of String): Integer;
    function getIndexForArrayMin1(S: Array of Integer): Integer;
    function getIndexForArrayMax(S: Array of Integer;
      SUsed: array of String): Integer;
    function getIndexForArrayMax1(S: Array of Integer;
      SUsed: array of String): Integer;
    function getIndexForArrayMax2(S: Array of Integer;
      SUsed: array of String): Integer;
    function getIndexForArrayMax3(S: Array of Integer;
      SUsed: array of String): Integer;
    function getMfForArray(DCount: Integer; D: Array of Integer;
      C: Array of Integer; RoleNum: Integer): Integer;
    function getMfForArray1(DCount: Integer; D: Array of Integer;
      C: Array of Integer; RoleNum: Integer): Integer;
    function CalDcount(D: TStringList): Integer;
    function CalDcountOther(D: array of Integer): Integer;
    function CalJsOther(D: array of Integer): Integer;
    procedure FillGridEmptyDetail(SAll, Kc: TStringList; MATERIALFZID: Integer;
      cmtypenum: Integer);
    procedure FillGridEmptyNoFzDetail(SAll, Kc: TStringList;
      MATERIALFZID: Integer; cmtypenum: Integer);
    procedure BubbleSort(var x: array of Integer);
    procedure BubbleSortOptimized(var x: array of Integer);
    procedure QuickSortOptimized(var x: array of Integer; low, high: Integer);
    function PartitionOptimized(var x: array of Integer; low, high: Integer): Integer;
    procedure FillGrid2(S, D1StringList, D2StringList,
      D3StringList: TStringList);
    procedure FillGrid2Five(S, D1StringList, D2StringList, D3StringList,
      D4StringList, D5StringList: TStringList);
    procedure FillGrid2Ten(S, D1StringList, D2StringList, D3StringList,
      D4StringList, D5StringList, D6StringList, D7StringList, D8StringList,
      D9StringList, D10StringList: TStringList);
    procedure FillGrid2Detail(SAll, Kc, D1, D2, D3: TStringList;
      MATERIALFZID: Integer; cmtypenum: Integer);
    procedure FillGrid2FiveDetail(SAll, Kc, D1, D2, D3, D4, D5: TStringList;
      MATERIALFZID: Integer; cmtypenum: Integer);
    procedure FillGrid2TenDetail(SAll, Kc, D1, D2, D3, D4, D5, D6, D7, D8, D9,
      D10: TStringList; MATERIALFZID: Integer; cmtypenum: Integer);
    function GetCmFzFromCmType(cmtypenum: Integer): TStringList;
  end;

var
  DMUtilSelect: TDataModule1;
  DMUtilSave: TDataModule1;
  FileStringList: TStringList;
  FileName, FilePath: string;
  FzMin_G: double;
  FzMax_G: double;
  CengShu: Integer;
  FNeedFive: Boolean;
  FNeedTen: Boolean;
  FmulFlag: Boolean;
  G_Js: string;
  G_Js_Max: string;
  G_Js_Min: string;
  G_Cs: string;
  G_bc: double;
  G_MaxTg: double;
  G_Gz1: string;
  G_Fz_B: string;
  G_Fz_E: string;
  G_YJL: double;
  G_ZLYJL: double;
  Source_Data1StringList: TStringList;
  Source_Data2StringList: TStringList;
  Source_Data3StringList: TStringList;
  Source_Data4StringList: TStringList;
  Source_Data5StringList: TStringList;
  Data1StringList: TStringList;
  Data2StringList: TStringList;
  Data3StringList: TStringList;
  Data4StringList: TStringList;
  Data5StringList: TStringList;
  DataStringList_Kc: TStringList;
  DataStringList_All: TStringList;
  G_MATERIALFZID_old: Integer;
  G_Materialid: Integer;
  G_Fztypenum: Integer;
  G_cmtypenum1: Integer;
  G_MaterialidStringList: TStringList;
  G_kstypesecid: Integer;
  SS1, SS2, SS3, SS4, SS5: Array of Integer;
  KS1, KS2, KS3, KS4, KS5: Array of Integer;
  SSALL1, SSALL2, SSALL3, SSALL4, SSALL5: Array of Integer;
  G_FormMaterialid: Integer;
  G_FormDdh: string;
  G_PdSuccessFlag: Boolean;
  G_PcAddNum: Integer;
  G_CalType: Integer;
  MoreKsFlag: Boolean;
  // 确定是男/女/儿 1/2/3
  G_CmTypenum: Integer;

Const
  CMPZSUM = 13;

implementation



{ TOptimizationHelper }

{ 类构造函数 - 初始化字符串缓存 }
class constructor TOptimizationHelper.Create;
begin
  FStringCache := TStringList.Create;
  FStringCache.Sorted := True;
  FStringCache.Duplicates := dupIgnore;
end;

{ 类析构函数 - 清理字符串缓存 }
class destructor TOptimizationHelper.Destroy;
begin
  FStringCache.Free;
end;

{ 获取缓存的整数值，避免重复的字符串转换 }
class function TOptimizationHelper.GetCachedIntValue(const AStr: string): Integer;
var
  Index: Integer;
begin
  if FStringCache.Find(AStr, Index) then
  begin
    Result := Integer(FStringCache.Objects[Index]);
  end
  else
  begin
    Result := StrToInt(AStr);
    FStringCache.AddObject(AStr, TObject(Result));
  end;
end;

{ 将TStringList转换为整数数组，使用缓存优化 }
class function TOptimizationHelper.StringListToIntArray(StringList: TStringList; StartIndex: Integer = 0; Count: Integer = -1): TArray<Integer>;
var
  i, EndIndex: Integer;
begin
  if Count = -1 then
    Count := StringList.Count - StartIndex;

  EndIndex := StartIndex + Count - 1;
  if EndIndex >= StringList.Count then
    EndIndex := StringList.Count - 1;

  SetLength(Result, EndIndex - StartIndex + 1);
  for i := StartIndex to EndIndex do
  begin
    Result[i - StartIndex] := GetCachedIntValue(StringList.Strings[i]);
  end;
end;

{ 计算TStringList中数值的总和，使用缓存优化 }
class function TOptimizationHelper.CalculateStringListSum(StringList: TStringList; StartIndex: Integer = 0; Count: Integer = -1): Integer;
var
  i, EndIndex: Integer;
begin
  Result := 0;
  if Count = -1 then
    Count := StringList.Count - StartIndex;

  EndIndex := StartIndex + Count - 1;
  if EndIndex >= StringList.Count then
    EndIndex := StringList.Count - 1;

  for i := StartIndex to EndIndex do
  begin
    Result := Result + GetCachedIntValue(StringList.Strings[i]);
  end;
end;

{ 清空缓存 }
class procedure TOptimizationHelper.ClearCache;
begin
  FStringCache.Clear;
end;

{ TThreadPdCal }

//procedure TThreadPdCal.BubbleSort(var x: array of Integer);
//var
//  i, j, intTmp: Integer;
//begin
//  for i := 0 to high(x) do
//  begin
//    for j := 0 to high(x) - 1 do
//    begin
//      if x[j] < x[j + 1] then
//      begin
//        intTmp := x[j];
//        x[j] := x[j + 1];
//        x[j + 1] := intTmp;
//      end;
//    end;
//  end;
//end;

{ 优化版本的排序算法 - 使用快速排序替代冒泡排序，时间复杂度从O(n?)降低到O(n log n) }
procedure TThreadPdCal.BubbleSort(var x: array of Integer);
begin
  QuickSortOptimized(x, 0, High(x));
end;

{ 快速排序的递归实现，降序排列 }
procedure TThreadPdCal.QuickSortOptimized(var x: array of Integer; low, high: Integer);
var
  pivotIndex: Integer;
begin
  if low < high then
  begin
    pivotIndex := PartitionOptimized(x, low, high);
    QuickSortOptimized(x, low, pivotIndex - 1);
    QuickSortOptimized(x, pivotIndex + 1, high);
  end;
end;

{ 快速排序的分区函数，实现降序排列 }
function TThreadPdCal.PartitionOptimized(var x: array of Integer; low, high: Integer): Integer;
var
  pivot, i, j, temp: Integer;
begin
  pivot := x[high]; // 选择最后一个元素作为基准
  i := low - 1;

  for j := low to high - 1 do
  begin
    if x[j] >= pivot then // 降序排列，大于等于基准的放左边
    begin
      Inc(i);
      temp := x[i];
      x[i] := x[j];
      x[j] := temp;
    end;
  end;

  temp := x[i + 1];
  x[i + 1] := x[high];
  x[high] := temp;

  Result := i + 1;
end;

function TThreadPdCal.CalDcount(D: TStringList): Integer;
var
  i, j: Integer;
  C: Array of Integer;
  DCount: Integer;
  DivFlag: Boolean;
  DArray: TArray<Integer>;
  G_Cs_Int: Integer;
  DValue: Integer;
begin
  setlength(C, 8);

  C[0] := 200;
  C[1] := 175;
  C[2] := 150;
  C[3] := 125;
  C[4] := 100;
  C[5] := 75;
  C[6] := 50;
  C[7] := 25;

  // 优化：预先转换所有字符串为整数，避免重复转换
  DArray := TOptimizationHelper.StringListToIntArray(D, 0, CMPZSUM);
  G_Cs_Int := TOptimizationHelper.GetCachedIntValue(G_Cs);

  DivFlag := true;
  DCount := 0;

  for i := 0 to 7 do
  begin
    if C[i] <= G_Cs_Int then
    begin

      if (DivFlag and (DCount > 0)) then
      begin
        break;
      end
      else
      begin
        DivFlag := true;
        DCount := 0;
        for j := 0 to CMPZSUM - 1 do
        begin
          DValue := DArray[j];
          if DValue > 0 then
          begin
            if (DValue mod C[i] = 0) then
            begin
              DCount := DCount + DValue div C[i];
            end
            else
            begin
              DivFlag := false;
              break;
            end;

          end;

        end;
      end;
    end;
  end;
  setlength(C, 0);
  result := DCount;

end;

function TThreadPdCal.CalDcountOther(D: array of Integer): Integer;
var
  i, j: Integer;
  C: Array of Integer;
  DCount: Integer;
  DivFlag: Boolean;
  G_Cs_Int: Integer;
begin
  setlength(C, 8);

  C[0] := 200;
  C[1] := 175;
  C[2] := 150;
  C[3] := 125;
  C[4] := 100;
  C[5] := 75;
  C[6] := 50;
  C[7] := 25;

  // 优化：缓存G_Cs的整数值，避免重复转换
  G_Cs_Int := TOptimizationHelper.GetCachedIntValue(G_Cs);

  DivFlag := true;
  DCount := 0;

  for i := 0 to 7 do
  begin
    if C[i] <= G_Cs_Int then
    begin
      if (DivFlag and (DCount > 0)) then
      begin
        break;
      end
      else
      begin
        DivFlag := true;
        DCount := 0;
        for j := 0 to CMPZSUM - 1 do
        begin
          if (D[j]) > 0 then
          begin
            if ((D[j]) mod C[i] = 0) then
            begin
              DCount := DCount + D[j] div C[i];
            end
            else
            begin
              DivFlag := false;
              break;
            end;

          end;

        end;
      end;
    end;
  end;

  setlength(C, 0);
  result := DCount;

end;

function TThreadPdCal.CalJsOther(D: array of Integer): Integer;
var
  i, j: Integer;
  C: Array of Integer;
  DCount: Integer;
  DivFlag: Boolean;
  js: Integer;
  G_Cs_Int: Integer;
begin
  setlength(C, 8);

  C[0] := 200;
  C[1] := 175;
  C[2] := 150;
  C[3] := 125;
  C[4] := 100;
  C[5] := 75;
  C[6] := 50;
  C[7] := 25;

  // 优化：缓存G_Cs的整数值，避免重复转换
  G_Cs_Int := TOptimizationHelper.GetCachedIntValue(G_Cs);

  DivFlag := true;
  DCount := 0;
  js := 0;
  for i := 0 to 7 do
  begin
    if C[i] <= G_Cs_Int then
    begin
      if (DivFlag and (DCount > 0)) then
      begin
        break;
      end
      else
      begin
        DivFlag := true;
        DCount := 0;
        for j := 0 to CMPZSUM - 1 do
        begin
          if (D[j]) > 0 then
          begin
            if ((D[j]) mod C[i] = 0) then
            begin
              DCount := DCount + D[j] div C[i];
              js := C[i];
            end
            else
            begin
              DivFlag := false;
              js := 0;
              break;
            end;

          end;

        end;
      end;
    end;
  end;

  result := js;

end;

function TThreadPdCal.CalOnceFiveSingle(changei: Integer; SIn: array of Integer;
  FlagType: Integer): TList;
var
  i, k, m, n, o, p: Integer;
  S: Array of Integer;
  S1: Array of Integer;
  S1Userd: Array of String;
  S2: Array of Integer;
  S2Userd: Array of String;
  S3: Array of Integer;
  S3Userd: Array of String;
  S4: Array of Integer;
  S4Userd: Array of String;
  S5: Array of Integer;
  S5Userd: Array of String;
  D1: Array of Integer;
  D2: Array of Integer;
  D3: Array of Integer;
  D4: Array of Integer;
  D5: Array of Integer;
  DCount, DCount1, DCount2, DCount3, DCount4, DCount5: Integer;
  maxindex, minindex: Integer;
  mtop: Integer;
  Role1: Array of Integer;
  Role2: Array of Integer;
  Role3: Array of Integer;
  Role4: Array of Integer;
  Role5: Array of Integer;
  flag: Integer;
  C: Array of Integer;
  List, ListFormat, ListFormatShow: TList;
  ReturnList, ReturnList_n: TList;
  ReturnStringList_n, ReturnStringList_Copy: TStringList;
  StringList_S, StringList_D, StringList_D1, StringList_D2, StringList_D3,
    StringList_D4, StringList_D5: TStringList;
  CStringList_S, CStringList_D, CStringList_D1, CStringList_D2, CStringList_D3,
    CStringList_D4, CStringList_D5: TStringList;
  Sum1: Integer;
  Sum2: Integer;
  SumD1, SumD2, SumD3, SumD4, SumD5: Integer;
  Js_G: Integer;
  SumStringList1, SumStringList2: TStringList;
  DCountMin, DCountMinIndex, DCountMinIndex1: Integer;
  CalDCountSum, CalDcount1, CalDCount2, CalDCount3, CalDCount4,
    CalDCount5: Integer;
  ItemIndex: Array of Integer;
  ItemCount: Integer;
  UsedIndex: Array of Integer;
  math75Flag: Boolean;
  r1, r2, r3, r4, r5: Integer;
  rcount: Integer;
  ListFilter1, ListFilter2, ListFilter3, ListFilter4, ListFilter5,
    ListFilterAll: TList;
  js: Integer;
  DsMin: Integer;
  DsItem: Integer;
  q: Integer;
  AveFlag: Boolean;
  maxindex1, maxindex2: Integer;
  maxindexcompare: Integer;
  Step1List: TStringList;
  AddRoleFlag1, AddRoleFlag2: Boolean;
  StringList_n: TStringList;
  S1Sum: Integer;
  SMin: Integer;
  CStringList: TStringList;
begin

  Js_G := StrToInt(G_Js);
  FzMin_G := StrToFloat(G_Fz_B);
  FzMax_G := StrToFloat(G_Fz_E);

  // 读取数据
  setlength(S, CMPZSUM);
  setlength(S1, CMPZSUM);
  setlength(S2, CMPZSUM);
  setlength(S3, CMPZSUM);
  setlength(S4, CMPZSUM);
  setlength(S5, CMPZSUM);
  setlength(D1, CMPZSUM);
  setlength(D2, CMPZSUM);
  setlength(D3, CMPZSUM);
  setlength(D4, CMPZSUM);
  setlength(D5, CMPZSUM);
  setlength(S1Userd, CMPZSUM);
  setlength(S2Userd, CMPZSUM);
  setlength(S3Userd, CMPZSUM);
  setlength(S4Userd, CMPZSUM);
  setlength(S5Userd, CMPZSUM);
  setlength(C, CMPZSUM);

  S[0] := SIn[0];
  S[1] := SIn[1];
  S[2] := SIn[2];
  S[3] := SIn[3];
  S[4] := SIn[4];
  S[5] := SIn[5];
  S[6] := SIn[6];
  S[7] := SIn[7];
  S[8] := SIn[8];
  S[9] := SIn[9];
  S[10] := SIn[10];
  S[11] := SIn[11];
  S[12] := SIn[12];

  // 根据数据库中配置取值
  CStringList := GetCmFzFromCmType(G_CmTypenum);
  for i := 0 to CStringList.Count - 1 do
  begin
    C[i] := StrToInt(CStringList.Strings[i]);
  end;

  // C[0] := 1;
  // C[1] := 2;
  // C[2] := 3;
  // C[3] := 4;
  // C[4] := 5;
  // C[5] := 6;
  // C[6] := 7;
  // C[7] := 8;
  // C[8] := 9;
  // C[9] := 10;
  // C[10] := 11;
  // C[11] := 12;
  // C[12] := 13;

  setlength(Role1, CengShu * CengShu * CengShu * CengShu * CengShu);
  setlength(Role2, CengShu * CengShu * CengShu * CengShu * CengShu);
  setlength(Role3, CengShu * CengShu * CengShu * CengShu * CengShu);
  setlength(Role4, CengShu * CengShu * CengShu * CengShu * CengShu);
  setlength(Role5, CengShu * CengShu * CengShu * CengShu * CengShu);

  rcount := 0;
  for r1 := 0 to CengShu - 1 do
  begin
    for r2 := 0 to CengShu - 1 do
    begin
      for r3 := 0 to CengShu - 1 do
      begin
        for r4 := 0 to CengShu - 1 do
        begin
          for r5 := 0 to CengShu - 1 do
          begin
            Role1[rcount] := (r1 + 1) * 25;
            Role2[rcount] := (r2 + 1) * 25;
            Role3[rcount] := (r3 + 1) * 25;
            Role4[rcount] := (r4 + 1) * 25;
            Role5[rcount] := (r5 + 1) * 25;
            rcount := rcount + 1;
          end;
        end;

      end;
    end;
  end;

  List := TList.Create;

  // 取当前数组最小值，也作为一个重要的判断依据

  SMin := S[self.getIndexForArrayMin1(S)];

  // 加载基础配置信息，计算平均分值，不符合就淘汰此方案
  // 已25为基数，200最大，递减，进行每个尺码的取整

  flag := 0;

  if StrToInt(G_Js) >= 0 then
  begin
    for q := 0 to StrToInt(G_Js) - 1 do

    // for q := 6 to 6 do
    begin
      Js_G := q + 1;

      for i := CengShu * CengShu * CengShu * CengShu * CengShu - 1 downto 0 do
      begin

        // 第一轮
        for k := 0 to CMPZSUM - 1 do
        begin
          S1[k] := S[k];
          D1[k] := 0;
        end;
        DCount1 := 0;
        for o := 0 to CMPZSUM - 1 do
        begin
          S1Userd[o] := 'N';
        end;

        // 这里做分支，处理多种情况，最终的结果都记录在list中

        // 流程1
        maxindexcompare := 0;
        S1Sum := 0;
        for m := 0 to CMPZSUM - 1 do
        begin
          if DCount1 <= Js_G - 1 then
          begin
            // 取当前最大序号
            if (changei = 4) and (self.getNumberForArrayMax(S1Userd) = 1) then
            begin
              maxindex1 := getIndexForArrayMax2(S1, S1Userd);
              maxindex2 := getIndexForArrayMax3(S1, S1Userd);
            end
            else
            begin
              maxindex1 := getIndexForArrayMax(S1, S1Userd);
              maxindex2 := getIndexForArrayMax1(S1, S1Userd);
            end;

            if maxindex1 = maxindex2 then
            begin
              maxindex := maxindex1;
              maxindexcompare := 0;
            end
            else
            begin
              if (m mod 2 = 0) then
              begin
                maxindex := maxindex1;
                maxindexcompare := 0;
              end
              else
              begin
                maxindexcompare := 1;
              end;
            end;

            if maxindex = -1 then
            begin
              break;
            end
            else
            begin
              if maxindexcompare = 1 then
              begin
                S1Userd[maxindex2] := 'Y';
                if S1[maxindex2] >= Role1[i] then
                begin
                  mtop := S1[maxindex2] div Role1[i];
                  for n := mtop downto 1 do
                  begin
                    if ((changei = 1) and ((S1[maxindex2] - Role1[i] * n)
                      mod (Role2[i]) = 0)) or
                      ((changei = 1) and ((S1[maxindex2] - Role1[i] * n)
                      mod (Role3[i]) = 0)) or
                      ((changei = 1) and ((S1[maxindex2] - Role1[i] * n) mod (1)
                      = 0)) or ((changei = 2) and
                      ((S1[maxindex2] - Role1[i] * n) mod (Role2[i] + Role3[i])
                      = 0)) or ((changei = 3) and
                      ((S1[maxindex2] - Role1[i] * n) = 0)) or
                      ((changei = 4) and ((S1[maxindex2] - Role1[i] * n)
                      mod (Role2[i]) = 0)) or
                      ((changei = 4) and ((S1[maxindex2] - Role1[i] * n)
                      mod (Role3[i]) = 0)) or
                      ((changei = 4) and ((S1[maxindex2] - Role1[i] * n) mod (1)
                      = 0)) then
                    begin
                      if DCount1 + n <= Js_G then
                      begin
                        if (S1Sum + Role1[i] * n) * G_YJL < G_MaxTg then
                        begin
                          S1[maxindex2] := S1[maxindex2] - Role1[i] * n;
                          D1[maxindex2] := Role1[i] * n;
                          DCount1 := DCount1 + n;
                          S1Sum := S1Sum + Role1[i] * n;
                          break;
                        end;
                      end;
                    end;
                  end;
                end;
              end
              else
              begin

                S1Userd[maxindex] := 'Y';
                if S1[maxindex] >= Role1[i] then
                begin
                  mtop := S1[maxindex] div Role1[i];
                  for n := mtop downto 1 do
                  begin
                    if ((changei = 1) and ((S1[maxindex] - Role1[i] * n)
                      mod (Role2[i]) = 0)) or
                      ((changei = 1) and ((S1[maxindex] - Role1[i] * n)
                      mod (Role3[i]) = 0)) or
                      ((changei = 1) and ((S1[maxindex] - Role1[i] * n) mod (1)
                      = 0)) or ((changei = 2) and
                      ((S1[maxindex] - Role1[i] * n) mod (Role2[i] + Role3[i])
                      = 0)) or ((changei = 3) and
                      ((S1[maxindex] - Role1[i] * n) = 0)) or
                      ((changei = 4) and ((S1[maxindex] - Role1[i] * n)
                      mod (Role2[i]) = 0)) or
                      ((changei = 4) and ((S1[maxindex] - Role1[i] * n)
                      mod (Role3[i]) = 0)) or
                      ((changei = 4) and ((S1[maxindex] - Role1[i] * n) mod (1)
                      = 0)) then
                    begin
                      if DCount1 + n <= Js_G then
                      begin
                        if (S1Sum + Role1[i] * n) * G_YJL < G_MaxTg then
                        begin
                          S1[maxindex] := S1[maxindex] - Role1[i] * n;
                          D1[maxindex] := Role1[i] * n;
                          DCount1 := DCount1 + n;
                          S1Sum := S1Sum + Role1[i] * n;
                          break;
                        end;
                      end;
                    end;
                  end;
                end;
              end;
            end;
          end;
        end;

        // 第一轮计算完毕后，进行门幅的校验
        flag := getMfForArray(DCount1, D1, C, Role1[i]);

        // 如果mf校验未通过，则需要进行校正
        if flag > 0 then
        begin
          if flag = 1 then
          begin
            for k := 0 to CMPZSUM - 1 do
            begin
              if D1[k] > 0 then
              begin
                S1[k] := S1[k] + D1[k];
                D1[k] := 0;
                break;
              end;
            end;
          end;

          if flag = 2 then
          begin
            for k := CMPZSUM - 1 downto 0 do
            begin
              if D1[k] > 0 then
              begin
                S1[k] := S1[k] + D1[k];
                D1[k] := 0;
                break;
              end;
            end;
          end;
          DCount1 := CalDcountOther(D1);
          flag := getMfForArray(DCount1, D1, C, Role1[i]);
        end;

        if flag = 0 then
        begin

          // 第二轮
          for k := 0 to CMPZSUM - 1 do
          begin
            S2[k] := S1[k];
            D2[k] := 0;
          end;

          if (S2[0] + S2[1] + S2[2] + S2[3] + S2[4] + S2[5] + S2[6] + S2[7] +
            S2[8] + S2[9] + S2[10] + S2[11] + S2[12]) > 0 then
          begin
            DCount2 := 0;
            for o := 0 to CMPZSUM - 1 do
            begin
              S2Userd[o] := 'N';
            end;
            for m := 0 to CMPZSUM - 1 do
            begin
              if DCount2 <= Js_G - 1 then
              begin
                // 取当前最大序号
                maxindex := getIndexForArrayMax(S2, S2Userd);
                if maxindex = -1 then
                begin
                  break;
                end
                else
                begin
                  S2Userd[maxindex] := 'Y';
                  if S2[maxindex] >= Role2[i] then
                  begin
                    mtop := S2[maxindex] div (Role2[i]);
                    for n := mtop downto 1 do
                    begin
                      if (Role3[i] = 200) or (Role3[i] = 100) or
                        (Role3[i] = 150) or (Role3[i] = 125) or (Role3[i] = 175)
                      then
                      begin
                        if ((S2[maxindex] - Role2[i] * n) mod (Role3[i]) = 0) or
                          ((S2[maxindex] - Role2[i] * n) mod (Role3[i] div 2)
                          = 0) or ((S2[maxindex] - Role2[i] * n)
                          mod (Role3[i] div 4) = 0) or
                          ((S2[maxindex] - Role2[i] * n) = 0) or
                          ((S2[maxindex] - Role2[i] * n) mod (SMin) = 0) then
                        begin
                          if DCount2 + n <= Js_G then
                          begin
                            S2[maxindex] := S2[maxindex] - Role2[i] * n;
                            D2[maxindex] := Role2[i] * n;
                            DCount2 := DCount2 + n;
                            break;
                          end;
                        end;
                      end
                      else
                      begin
                        if ((S2[maxindex] - Role2[i] * n) mod (Role3[i]) = 0) or
                          ((S2[maxindex] - Role2[i] * n) = 0) or
                          ((S2[maxindex] - Role2[i] * n)
                          mod (Role3[i] div 2) = 0) then
                        begin
                          if DCount2 + n <= Js_G then
                          begin
                            S2[maxindex] := S2[maxindex] - Role2[i] * n;
                            D2[maxindex] := Role2[i] * n;
                            DCount2 := DCount2 + n;
                            break;
                          end;
                        end;
                      end;
                    end;
                  end;
                end;
              end;
            end;

            flag := getMfForArray(DCount2, D2, C, Role2[i]);

            // 如果mf校验未通过，则需要进行校正
            if flag > 0 then
            begin
              if flag = 1 then
              begin
                for k := 0 to CMPZSUM - 1 do
                begin
                  if D2[k] > 0 then
                  begin
                    S2[k] := S2[k] + D2[k];
                    D2[k] := 0;
                    break;
                  end;
                end;
              end;

              if flag = 2 then
              begin
                for k := CMPZSUM - 1 downto 0 do
                begin
                  if D2[k] > 0 then
                  begin
                    S2[k] := S2[k] + D2[k];
                    D2[k] := 0;
                    break;
                  end;
                end;
              end;
              DCount2 := CalDcountOther(D2);
              flag := getMfForArray(DCount2, D2, C, Role2[i]);
            end;
          end;

        end;

        if flag = 0 then
        begin

          // 第三轮
          for k := 0 to CMPZSUM - 1 do
          begin
            S3[k] := S2[k];
            D3[k] := 0;
          end;

          if (S3[0] + S3[1] + S3[2] + S3[3] + S3[4] + S3[5] + S3[6] + S3[7] +
            S3[8] + S3[9] + S3[10] + S3[11] + S3[12]) > 0 then
          begin
            DCount3 := 0;
            for o := 0 to CMPZSUM - 1 do
            begin
              S3Userd[o] := 'N';
            end;
            for m := 0 to CMPZSUM - 1 do
            begin
              if DCount3 <= Js_G - 1 then
              begin
                // 取当前最大序号
                maxindex := getIndexForArrayMax(S3, S3Userd);
                if maxindex = -1 then
                begin
                  break;
                end
                else
                begin
                  S3Userd[maxindex] := 'Y';
                  if S3[maxindex] >= Role3[i] then
                  begin
                    mtop := S3[maxindex] div (Role3[i]);
                    for n := mtop downto 1 do
                    begin
                      if ((S3[maxindex] - Role3[i] * n) mod (Role3[i]) = 0) or
                        ((S3[maxindex] - Role3[i] * n) mod (Role3[i] div 2) = 0)
                        or ((S3[maxindex] - Role3[i] * n) = 0) or
                        ((S3[maxindex] - Role3[i] * n) mod (SMin) = 0) then
                      begin
                        if DCount3 + n <= Js_G then
                        begin
                          S3[maxindex] := S3[maxindex] - Role3[i] * n;
                          D3[maxindex] := Role3[i] * n;
                          DCount3 := DCount3 + n;
                          break;
                        end;
                      end;
                    end;
                  end;
                end;
              end;
            end;

            flag := getMfForArray(DCount3, D3, C, Role3[i]);

            // 如果mf校验未通过，则需要进行校正
            if flag > 0 then
            begin
              if flag = 1 then
              begin
                for k := 0 to CMPZSUM - 1 do
                begin
                  if D3[k] > 0 then
                  begin
                    S3[k] := S3[k] + D3[k];
                    D3[k] := 0;
                    break;
                  end;
                end;
              end;

              if flag = 2 then
              begin
                for k := CMPZSUM - 1 downto 0 do
                begin
                  if D3[k] > 0 then
                  begin
                    S3[k] := S3[k] + D3[k];
                    D3[k] := 0;
                    break;
                  end;
                end;
              end;
              DCount3 := CalDcountOther(D3);
              flag := getMfForArray(DCount3, D3, C, Role3[i]);
              if flag <> 0 then
              begin
                if (Role3[i] = 200) or (Role3[i] = 100) then
                begin
                  begin
                    flag := getMfForArray(DCount, D3, C, Role3[i] div 2);
                  end;
                  if flag <> 0 then
                  begin
                    flag := getMfForArray(DCount, D3, C, Role3[i] div 4);
                  end;
                end;
              end;
            end;
          end;

        end;

        if flag = 0 then
        begin

          // 第4轮
          for k := 0 to CMPZSUM - 1 do
          begin
            S4[k] := S3[k];
            D4[k] := 0;
          end;

          if (S4[0] + S4[1] + S4[2] + S4[3] + S4[4] + S4[5] + S4[6] + S4[7] +
            S4[8] + S4[9] + S4[10] + S4[11] + S4[12]) > 0 then
          begin
            DCount4 := 0;
            for o := 0 to CMPZSUM - 1 do
            begin
              S4Userd[o] := 'N';
            end;
            for m := 0 to CMPZSUM - 1 do
            begin
              if DCount4 <= Js_G - 1 then
              begin
                // 取当前最大序号
                maxindex := getIndexForArrayMax(S4, S4Userd);
                if maxindex = -1 then
                begin
                  break;
                end
                else
                begin
                  S4Userd[maxindex] := 'Y';
                  if S4[maxindex] >= Role4[i] then
                  begin
                    mtop := S4[maxindex] div (Role4[i]);
                    for n := mtop downto 1 do
                    begin
                      if ((S4[maxindex] - Role4[i] * n) mod (Role3[i]) = 0) or
                        ((S4[maxindex] - Role4[i] * n) = 0) then
                      begin
                        if DCount4 + n <= Js_G then
                        begin
                          S4[maxindex] := S4[maxindex] - Role4[i] * n;
                          D4[maxindex] := Role4[i] * n;
                          DCount4 := DCount4 + n;
                          break;
                        end;
                      end;
                    end;
                  end;
                end;
              end;
            end;

            flag := getMfForArray(DCount4, D4, C, Role4[i]);

            // 如果mf校验未通过，则需要进行校正
            if flag > 0 then
            begin
              if flag = 1 then
              begin
                for k := 0 to CMPZSUM - 1 do
                begin
                  if D4[k] > 0 then
                  begin
                    S4[k] := S4[k] + D4[k];
                    D4[k] := 0;
                    break;
                  end;
                end;
              end;

              if flag = 2 then
              begin
                for k := CMPZSUM - 1 downto 0 do
                begin
                  if D4[k] > 0 then
                  begin
                    S4[k] := S4[k] + D4[k];
                    D4[k] := 0;
                    break;
                  end;
                end;
              end;
              DCount4 := CalDcountOther(D4);
              flag := getMfForArray(DCount4, D4, C, Role4[i]);
            end;
          end;

        end;

        if flag = 0 then
        begin
          // 第五轮
          for k := 0 to CMPZSUM - 1 do
          begin
            S5[k] := S4[k];
            D5[k] := 0;
          end;

          if (S5[0] + S5[1] + S5[2] + S5[3] + S5[4] + S5[5] + S5[6] + S5[7] +
            S5[8] + S5[9] + S5[10] + S5[11] + S5[12]) > 0 then
          begin
            DCount5 := 0;
            for o := 0 to CMPZSUM - 1 do
            begin
              S5Userd[o] := 'N';
            end;
            for m := 0 to CMPZSUM - 1 do
            begin
              if DCount5 <= Js_G - 1 then
              begin
                // 取当前最大序号
                maxindex := getIndexForArrayMax(S5, S5Userd);
                if maxindex = -1 then
                begin
                  break;
                end
                else
                begin
                  S5Userd[maxindex] := 'Y';
                  // 第5次序算法又不同了
                  D5[maxindex] := S5[maxindex];
                  S5[maxindex] := S5[maxindex] - S5[maxindex];
                  DCount5 := DCount5 + 1;
                end;
              end;

            end;

            if flag = 0 then
            begin
              // 第五轮做个校验，不理想则结果不用显示
              for o := 0 to CMPZSUM - 1 do
              begin
                S5Userd[o] := 'N';
              end;
              maxindex := getIndexForArrayMax(D5, S5Userd);
              minindex := getIndexForArrayMin(D5, S5Userd);
              if D5[minindex] > 0 then
              begin
                if (D5[maxindex] div D5[minindex] >= 5) or
                  (D5[maxindex] mod D5[minindex] <> 0) then
                begin
                  flag := 1;
                end
                else
                begin
                  DCount := 0;
                  if (D5[maxindex] mod D5[minindex] = 0) then
                  begin
                    for o := 0 to CMPZSUM - 1 do
                    begin
                      DCount := DCount + (D5[o] div D5[minindex]);
                    end;
                    if DCount > Js_G then
                    begin
                      flag := 1;
                    end;
                    if flag = 0 then
                    begin
                      flag := getMfForArray(DCount, D5, C, Role5[i]);
                    end;
                  end;
                end
              end;
            end;
          end;

          if flag = 0 then
          begin
            DCount5 := CalDcountOther(D5);
            js := CalJsOther(D5);
            if DCount5 > Js_G then
            begin
              flag := 1;
            end
            else
            begin
              flag := getMfForArray1(DCount5, D5, C, js);
            end;
          end;
        end;

        if flag = 0 then
        begin
          // WriteImportLog1('C:\1.txt', '1' + '<<<qqqqqq<<<' +
          // format('memory use: %d KB',
          // [GetProcessMemUse(GetCurrentProcessId)]));
          //
          // 最后在校验下是否全部分配掉了
          Sum1 := 0;
          Sum2 := 0;
          SumD1 := 0;
          SumD2 := 0;
          SumD3 := 0;
          SumD4 := 0;
          SumD5 := 0;

          StringList_S := TStringList.Create;
          StringList_D1 := TStringList.Create;
          StringList_D2 := TStringList.Create;
          StringList_D3 := TStringList.Create;
          StringList_D4 := TStringList.Create;
          StringList_D5 := TStringList.Create;

          for o := 0 to CMPZSUM - 1 do
          begin
            StringList_S.Add(IntToStr(S[o]));
            StringList_D1.Add(IntToStr(D1[o]));
            StringList_D2.Add(IntToStr(D2[o]));
            StringList_D3.Add(IntToStr(D3[o]));
            StringList_D4.Add(IntToStr(D4[o]));
            StringList_D5.Add(IntToStr(D5[o]));
            Sum1 := Sum1 + S[o];
            Sum2 := Sum2 + D1[o] + D2[o] + D3[o] + D4[o] + D5[o];
            SumD1 := SumD1 + D1[o];
            SumD2 := SumD2 + D2[o];
            SumD3 := SumD3 + D3[o];
            SumD4 := SumD4 + D4[o];
            SumD5 := SumD5 + D5[o];
          end;

          if Sum1 = Sum2 then
          begin
            // 重新计算每个Dcount

            CalDcount1 := CalDcount(StringList_D1);
            CalDCount2 := CalDcount(StringList_D2);
            CalDCount3 := CalDcount(StringList_D3);
            CalDCount4 := CalDcount(StringList_D4);
            CalDCount5 := CalDcount(StringList_D5);

            CalDCountSum := CalDcount1 + CalDCount2 + CalDCount3 + CalDCount4 +
              CalDCount5;

            StringList_S.Add(IntToStr(CalDCountSum));
            StringList_D1.Add(IntToStr(CalDcount1));
            StringList_D2.Add(IntToStr(CalDCount2));
            StringList_D3.Add(IntToStr(CalDCount3));
            StringList_D4.Add(IntToStr(CalDCount4));
            StringList_D5.Add(IntToStr(CalDCount5));
            StringList_D1.Add(IntToStr(SumD1));
            StringList_D2.Add(IntToStr(SumD2));
            StringList_D3.Add(IntToStr(SumD3));
            StringList_D4.Add(IntToStr(SumD4));
            StringList_D5.Add(IntToStr(SumD5));

            math75Flag := true;

            if CalDcount1 > 1 then
            begin
              if (SumD1 / CalDcount1 > 75) then
              begin

                if (CalDcount1 >= 3) then
                begin
                  math75Flag := true;
                end
                else
                begin
                  math75Flag := false;
                end;

              end;
            end;

            if math75Flag then
            begin
              if CalDCount2 > 1 then
              begin
                if (SumD2 / CalDCount2 > 75) then
                begin
                  if (CalDCount2 >= 3) then
                  begin
                    math75Flag := true;
                  end
                  else
                  begin
                    math75Flag := false;
                  end;
                end;
              end;
            end;

            if math75Flag then
            begin
              if CalDCount3 > 1 then
              begin
                if (SumD3 / CalDCount3 > 75) then
                begin
                  if (CalDCount3 >= 3) then
                  begin
                    math75Flag := true;
                  end
                  else
                  begin
                    math75Flag := false;
                  end;
                end;
              end;
            end;

            if math75Flag then
            begin
              if CalDCount4 > 1 then
              begin
                if (SumD4 / CalDCount4 > 75) then
                begin
                  if (CalDCount4 >= 3) then
                  begin
                    math75Flag := true;
                  end
                  else
                  begin
                    math75Flag := false;
                  end;
                end;
              end;
            end;

            if math75Flag then
            begin
              if CalDCount5 > 1 then
              begin
                if (SumD5 / CalDCount5 > 75) then
                begin
                  if (CalDCount5 >= 3) then
                  begin
                    math75Flag := true;
                  end
                  else
                  begin
                    math75Flag := false;
                  end;
                end;
              end;
            end;

            // 这里对于重复数据的判断是否多余，还需要做个验证
            if math75Flag = true then
            begin

              AddRoleFlag1 := false;
              AddRoleFlag2 := false;
              // 加入判断1 是否满足最大投缸量
              // 加入判断2 如果有规则1369，则需要计算
              if (SumD1 * G_YJL < G_MaxTg) and (SumD2 * G_YJL < G_MaxTg) and
                (SumD3 * G_YJL < G_MaxTg) and (SumD4 * G_YJL < G_MaxTg) and
                (SumD5 * G_YJL < G_MaxTg) then
              begin
                AddRoleFlag1 := true;
              end;

              if G_Gz1 = '369排' then
              begin
                if ((CalDcount1 = 0) or (CalDcount1 = 1) or
                  ((CalDcount1 mod 3) = 0)) and
                  ((CalDCount2 = 0) or (CalDCount2 = 1) or
                  ((CalDCount2 mod 3) = 0)) and
                  ((CalDCount3 = 0) or (CalDCount3 = 1) or
                  ((CalDCount3 mod 3) = 0)) and
                  ((CalDCount4 = 0) or (CalDCount4 = 1) or
                  ((CalDCount4 mod 3) = 0)) and
                  ((CalDCount5 = 0) or (CalDCount5 = 1) or
                  ((CalDCount5 mod 3) = 0)) then
                begin
                  AddRoleFlag2 := true;
                end;
              end
              else
              begin
                if ((CalDcount1 = 0) or (CalDcount1 > 1)) and
                  ((CalDCount2 = 0) or (CalDCount2 > 1)) and
                  ((CalDCount3 = 0) or (CalDCount3 > 1)) and
                  ((CalDCount4 = 0) or (CalDCount4 > 1)) and
                  ((CalDCount5 = 0) or (CalDCount5 > 1)) then
                begin
                  AddRoleFlag2 := true;
                end
                else
                begin
                  AddRoleFlag2 := false;
                end;
              end;

              if AddRoleFlag1 and AddRoleFlag2 then
              begin

                CStringList_S := TStringList.Create;
                CStringList_D1 := TStringList.Create;
                CStringList_D2 := TStringList.Create;
                CStringList_D3 := TStringList.Create;
                CStringList_D4 := TStringList.Create;
                CStringList_D5 := TStringList.Create;

                for o := 0 to StringList_S.Count - 1 do
                begin
                  CStringList_S.Add(StringList_S.Strings[o]);
                end;

                for o := 0 to StringList_D1.Count - 1 do
                begin
                  CStringList_D1.Add(StringList_D1.Strings[o]);
                end;

                for o := 0 to StringList_D2.Count - 1 do
                begin
                  CStringList_D2.Add(StringList_D2.Strings[o]);
                end;

                for o := 0 to StringList_D3.Count - 1 do
                begin
                  CStringList_D3.Add(StringList_D3.Strings[o]);
                end;

                for o := 0 to StringList_D4.Count - 1 do
                begin
                  CStringList_D4.Add(StringList_D4.Strings[o]);
                end;

                for o := 0 to StringList_D5.Count - 1 do
                begin
                  CStringList_D5.Add(StringList_D5.Strings[o]);
                end;

                List.Add(CStringList_S);
                List.Add(CStringList_D1);
                List.Add(CStringList_D2);
                List.Add(CStringList_D3);
                List.Add(CStringList_D4);
                List.Add(CStringList_D5);

              end;

            end;

          end;
          FreeAndNil(StringList_S);
          FreeAndNil(StringList_D1);
          FreeAndNil(StringList_D2);
          FreeAndNil(StringList_D3);
          FreeAndNil(StringList_D4);
          FreeAndNil(StringList_D5);

        end;

      end;

    end;

  end;

  // 重置数据
  setlength(S, 0);
  setlength(S1, 0);
  setlength(S2, 0);
  setlength(S3, 0);
  setlength(S4, 0);
  setlength(S5, 0);
  setlength(D1, 0);
  setlength(D2, 0);
  setlength(D3, 0);
  setlength(D4, 0);
  setlength(D5, 0);
  setlength(S1Userd, 0);
  setlength(S2Userd, 0);
  setlength(S3Userd, 0);
  setlength(S4Userd, 0);
  setlength(S5Userd, 0);
  setlength(C, 0);

  setlength(Role1, 0);
  setlength(Role2, 0);
  setlength(Role3, 0);
  setlength(Role4, 0);
  setlength(Role5, 0);

  if FlagType = 1 then
  begin
    result := List;
  end
  else
  begin
    // 根据当前list取最好的结果

    ReturnList := FillOnceFiveReturn(List, 2);
    ReturnList_n := TList.Create;
    for i := 0 to ReturnList.Count - 1 do
    begin
      ReturnStringList_n := TStringList(ReturnList.Items[i]);
      ReturnStringList_Copy := TStringList.Create;
      for o := 0 to ReturnStringList_n.Count - 1 do
      begin
        ReturnStringList_Copy.Add(ReturnStringList_n.Strings[o]);
      end;
      ReturnList_n.Add(ReturnStringList_Copy);
    end;

    for i := 0 to List.Count - 1 do
    begin
      StringList_n := TStringList(List.Items[i]);
      StringList_n.Free;
    end;

    FreeAndNil(List);

    result := ReturnList_n;
  end;

end;

function TThreadPdCal.CalOnceSingle(changei: Integer; SIn: array of Integer;
  FlagType: Integer): TList;
var
  i, k, m, n, o, p: Integer;
  S: Array of Integer;
  S1: Array of Integer;
  S1Userd: Array of String;
  S2: Array of Integer;
  S2Userd: Array of String;
  S3: Array of Integer;
  S3Userd: Array of String;
  D1: Array of Integer;
  D2: Array of Integer;
  D3: Array of Integer;
  DCount, DCount1, DCount2, DCount3: Integer;
  maxindex, minindex: Integer;
  mtop: Integer;
  Role1: Array of Integer;
  Role2: Array of Integer;
  Role3: Array of Integer;
  flag: Integer;
  C: Array of Integer;
  List, ListFormat, ListFormatShow: TList;
  ReturnList, ReturnList_n: TList;
  ReturnStringList_n, ReturnStringList_Copy: TStringList;
  StringList_S, StringList_D, StringList_D1, StringList_D2,
    StringList_D3: TStringList;
  StringList_n: TStringList;
  CStringList_S, CStringList_D, CStringList_D1, CStringList_D2,
    CStringList_D3: TStringList;
  Sum1: Integer;
  Sum2: Integer;
  SumD1, SumD2, SumD3: Integer;
  Js_G: Integer;
  SumStringList1, SumStringList2: TStringList;
  DCountMin, DCountMinIndex, DCountMinIndex1: Integer;
  CalDCountSum, CalDcount1, CalDCount2, CalDCount3: Integer;
  ItemIndex: Array of Integer;
  ItemCount: Integer;
  UsedIndex: Array of Integer;
  math75Flag: Boolean;
  r1, r2, r3: Integer;
  rcount: Integer;
  ListFilter1, ListFilter2, ListFilter3, ListFilterAll: TList;
  js: Integer;
  DsMin: Integer;
  DsItem: Integer;
  q: Integer;
  AveFlag: Boolean;
  maxindex1, maxindex2: Integer;
  maxindexcompare: Integer;
  Step1List: TStringList;
  AddRoleFlag1, AddRoleFlag2: Boolean;
  S1Sum: Integer;
  SMin: Integer;
  CStringList: TStringList;
begin

  Js_G := StrToInt(G_Js);
  FzMin_G := StrToFloat(G_Fz_B);
  FzMax_G := StrToFloat(G_Fz_E);

  // 读取数据
  setlength(S, CMPZSUM);
  setlength(S1, CMPZSUM);
  setlength(S2, CMPZSUM);
  setlength(S3, CMPZSUM);
  setlength(D1, CMPZSUM);
  setlength(D2, CMPZSUM);
  setlength(D3, CMPZSUM);
  setlength(S1Userd, CMPZSUM);
  setlength(S2Userd, CMPZSUM);
  setlength(S3Userd, CMPZSUM);
  setlength(C, CMPZSUM);

  S[0] := SIn[0];
  S[1] := SIn[1];
  S[2] := SIn[2];
  S[3] := SIn[3];
  S[4] := SIn[4];
  S[5] := SIn[5];
  S[6] := SIn[6];
  S[7] := SIn[7];
  S[8] := SIn[8];
  S[9] := SIn[9];
  S[10] := SIn[10];
  S[11] := SIn[11];
  S[12] := SIn[12];

  CStringList := GetCmFzFromCmType(G_CmTypenum);
  for i := 0 to CStringList.Count - 1 do
  begin
    C[i] := StrToInt(CStringList.Strings[i]);
  end;
  // C[0] := 1;
  // C[1] := 2;
  // C[2] := 3;
  // C[3] := 4;
  // C[4] := 5;
  // C[5] := 6;
  // C[6] := 7;
  // C[7] := 8;
  // C[8] := 9;
  // C[9] := 10;
  // C[10] := 11;
  // C[11] := 12;
  // C[12] := 13;

  setlength(Role1, CengShu * CengShu * CengShu);
  setlength(Role2, CengShu * CengShu * CengShu);
  setlength(Role3, CengShu * CengShu * CengShu);

  rcount := 0;
  for r1 := 0 to CengShu - 1 do
  begin
    for r2 := 0 to CengShu - 1 do
    begin
      for r3 := 0 to CengShu - 1 do
      begin
        Role1[rcount] := (r1 + 1) * 25;
        Role2[rcount] := (r2 + 1) * 25;
        Role3[rcount] := (r3 + 1) * 25;
        rcount := rcount + 1;

      end;
    end;
  end;

  List := TList.Create;
  SMin := S[self.getIndexForArrayMin1(S)];

  // 加载基础配置信息，计算平均分值，不符合就淘汰此方案
  // 已25为基数，200最大，递减，进行每个尺码的取整
  flag := 0;

  if StrToInt(G_Js) >= 0 then
  begin
    // for q := 6 to StrToInt(self.RzEdit2.Text) - 1 do
    for q := 0 to StrToInt(G_Js) - 1 do
    begin
      Js_G := q + 1;

      for i := CengShu * CengShu * CengShu - 1 downto 0 do
      begin
        // 第一轮
        for k := 0 to CMPZSUM - 1 do
        begin
          S1[k] := S[k];
          D1[k] := 0;
        end;
        DCount1 := 0;
        for o := 0 to CMPZSUM - 1 do
        begin
          S1Userd[o] := 'N';
        end;

        // 这里做分支，处理多种情况，最终的结果都记录在list中

        // // xyx
        // if (Js_G = 10) and (Role1[i] = 75) and (Role2[i] = 25) then
        // begin
        // WriteImportLog1('C:\1.txt', '1' + '<<<qqqqqq<<<');
        // end;

        // 流程1
        maxindexcompare := 0;
        S1Sum := 0;
        for m := 0 to CMPZSUM - 1 do
        begin
          if DCount1 <= Js_G - 1 then
          begin

            // 取当前最大序号
            if (changei = 4) and (self.getNumberForArrayMax(S1Userd) = 1) then
            begin
              maxindex1 := getIndexForArrayMax2(S1, S1Userd);
              maxindex2 := getIndexForArrayMax3(S1, S1Userd);
            end
            else
            begin
              maxindex1 := getIndexForArrayMax(S1, S1Userd);
              maxindex2 := getIndexForArrayMax1(S1, S1Userd);
            end;

            if maxindex1 = maxindex2 then
            begin
              maxindex := maxindex1;
              maxindexcompare := 0;
            end
            else
            begin
              if (m mod 2 = 0) then
              begin
                maxindex := maxindex1;
                maxindexcompare := 0;
              end
              else
              begin
                maxindexcompare := 1;
              end;
            end;

            if maxindex = -1 then
            begin
              break;
            end
            else
            begin
              if maxindexcompare = 1 then
              begin
                S1Userd[maxindex2] := 'Y';

                if S1[maxindex2] >= Role1[i] then
                begin
                  mtop := S1[maxindex2] div Role1[i];
                  for n := mtop downto 1 do
                  begin
                    if ((changei = 1) and ((S1[maxindex2] - Role1[i] * n)
                      mod (Role2[i]) = 0)) or
                      ((changei = 1) and ((S1[maxindex2] - Role1[i] * n)
                      mod (Role3[i]) = 0)) or
                      ((changei = 1) and ((S1[maxindex2] - Role1[i] * n) mod (1)
                      = 0)) or ((changei = 2) and
                      ((S1[maxindex2] - Role1[i] * n) mod (Role2[i] + Role3[i])
                      = 0)) or ((changei = 3) and
                      ((S1[maxindex2] - Role1[i] * n) = 0)) or
                      ((changei = 4) and ((S1[maxindex2] - Role1[i] * n)
                      mod (Role2[i]) = 0)) or
                      ((changei = 4) and ((S1[maxindex2] - Role1[i] * n)
                      mod (Role3[i]) = 0)) or
                      ((changei = 4) and ((S1[maxindex2] - Role1[i] * n) mod (1)
                      = 0)) then
                    begin
                      if DCount1 + n <= Js_G then
                      begin
                        if (S1Sum + Role1[i] * n) * G_YJL < G_MaxTg then
                        begin
                          S1[maxindex2] := S1[maxindex2] - Role1[i] * n;
                          D1[maxindex2] := Role1[i] * n;
                          DCount1 := DCount1 + n;
                          S1Sum := S1Sum + Role1[i] * n;
                          break;
                        end;
                      end;
                    end;
                  end;
                end;
              end
              else
              begin

                S1Userd[maxindex] := 'Y';
                if S1[maxindex] >= Role1[i] then
                begin
                  mtop := S1[maxindex] div Role1[i];
                  for n := mtop downto 1 do
                  begin

                    if ((changei = 1) and ((S1[maxindex] - Role1[i] * n)
                      mod (Role2[i]) = 0)) or
                      ((changei = 1) and ((S1[maxindex] - Role1[i] * n)
                      mod (Role3[i]) = 0)) or
                      ((changei = 1) and ((S1[maxindex] - Role1[i] * n) mod (1)
                      = 0)) or ((changei = 2) and
                      ((S1[maxindex] - Role1[i] * n) mod (Role2[i] + Role3[i])
                      = 0)) or ((changei = 3) and
                      ((S1[maxindex] - Role1[i] * n) = 0)) or
                      ((changei = 4) and ((S1[maxindex] - Role1[i] * n)
                      mod (Role2[i]) = 0)) or
                      ((changei = 4) and ((S1[maxindex] - Role1[i] * n)
                      mod (Role3[i]) = 0)) or
                      ((changei = 4) and ((S1[maxindex] - Role1[i] * n) mod (1)
                      = 0)) then
                    begin
                      if DCount1 + n <= Js_G then
                      begin
                        if (S1Sum + Role1[i] * n) * G_YJL < G_MaxTg then
                        begin
                          S1[maxindex] := S1[maxindex] - Role1[i] * n;
                          D1[maxindex] := Role1[i] * n;
                          DCount1 := DCount1 + n;
                          S1Sum := S1Sum + Role1[i] * n;
                          break;
                        end;
                      end;
                    end;
                  end;
                end;
              end;
            end;
          end;
        end;

        // 第一轮计算完毕后，进行门幅的校验
        flag := getMfForArray(DCount1, D1, C, Role1[i]);

        // 如果mf校验未通过，则需要进行校正
        if flag > 0 then
        begin
          if flag = 1 then
          begin
            for k := 0 to CMPZSUM - 1 do
            begin
              if D1[k] > 0 then
              begin
                S1[k] := S1[k] + D1[k];
                D1[k] := 0;
                break;
              end;
            end;
          end;

          if flag = 2 then
          begin
            for k := CMPZSUM - 1 downto 0 do
            begin
              if D1[k] > 0 then
              begin
                S1[k] := S1[k] + D1[k];
                D1[k] := 0;
                break;
              end;
            end;
          end;
          DCount1 := CalDcountOther(D1);
          flag := getMfForArray(DCount1, D1, C, Role1[i]);
        end;

        if flag = 0 then
        begin

          // 第二轮
          for k := 0 to CMPZSUM - 1 do
          begin
            S2[k] := S1[k];
            D2[k] := 0;
          end;

          if (S2[0] + S2[1] + S2[2] + S2[3] + S2[4] + S2[5] + S2[6] + S2[7] +
            S2[8] + S2[9] + S2[10] + S2[11] + S2[12]) > 0 then
          begin
            DCount2 := 0;
            for o := 0 to CMPZSUM - 1 do
            begin
              S2Userd[o] := 'N';
            end;
            for m := 0 to CMPZSUM - 1 do
            begin
              if DCount2 <= Js_G - 1 then
              begin
                // 取当前最大序号
                maxindex := getIndexForArrayMax(S2, S2Userd);
                if maxindex = -1 then
                begin
                  break;
                end
                else
                begin
                  S2Userd[maxindex] := 'Y';
                  if S2[maxindex] >= Role2[i] then
                  begin
                    mtop := S2[maxindex] div (Role2[i]);
                    for n := mtop downto 1 do
                    begin
                      if (Role3[i] = 200) or (Role3[i] = 100) or
                        (Role3[i] = 150) or (Role3[i] = 125) or (Role3[i] = 175)
                      then
                      begin
                        if ((S2[maxindex] - Role2[i] * n) mod (Role3[i]) = 0) or
                          ((S2[maxindex] - Role2[i] * n) mod (Role3[i] div 2)
                          = 0) or ((S2[maxindex] - Role2[i] * n)
                          mod (Role3[i] div 4) = 0) or
                          ((S2[maxindex] - Role2[i] * n) = 0) or
                          ((S2[maxindex] - Role2[i] * n) mod (SMin) = 0) then
                        begin
                          if DCount2 + n <= Js_G then
                          begin
                            S2[maxindex] := S2[maxindex] - Role2[i] * n;
                            D2[maxindex] := Role2[i] * n;
                            DCount2 := DCount2 + n;
                            break;
                          end;
                        end;
                      end
                      else
                      begin
                        if ((S2[maxindex] - Role2[i] * n) mod (Role3[i]) = 0) or
                          ((S2[maxindex] - Role2[i] * n) = 0) or
                          ((S2[maxindex] - Role2[i] * n)
                          mod (Role3[i] div 2) = 0) then
                        begin
                          if DCount2 + n <= Js_G then
                          begin
                            S2[maxindex] := S2[maxindex] - Role2[i] * n;
                            D2[maxindex] := Role2[i] * n;
                            DCount2 := DCount2 + n;
                            break;
                          end;
                        end;
                      end;

                    end;
                  end;
                end;
              end;
            end;

            flag := getMfForArray(DCount2, D2, C, Role2[i]);

            // 如果mf校验未通过，则需要进行校正
            if flag > 0 then
            begin
              if flag = 1 then
              begin
                for k := 0 to CMPZSUM - 1 do
                begin
                  if D2[k] > 0 then
                  begin
                    S2[k] := S2[k] + D2[k];
                    D2[k] := 0;
                    break;
                  end;
                end;
              end;

              if flag = 2 then
              begin
                for k := CMPZSUM - 1 downto 0 do
                begin
                  if D2[k] > 0 then
                  begin
                    S2[k] := S2[k] + D2[k];
                    D2[k] := 0;
                    break;
                  end;
                end;
              end;
              DCount2 := CalDcountOther(D2);
              flag := getMfForArray(DCount2, D2, C, Role2[i]);
            end;
          end;

        end;

        if flag = 0 then
        begin
          // 第三轮
          for k := 0 to CMPZSUM - 1 do
          begin
            S3[k] := S2[k];
            D3[k] := 0;
          end;

          if (S3[0] + S3[1] + S3[2] + S3[3] + S3[4] + S3[5] + S3[6] + S3[7] +
            S3[8] + S3[9] + S3[10] + S3[11] + S3[12]) > 0 then
          begin
            DCount3 := 0;
            for o := 0 to CMPZSUM - 1 do
            begin
              S3Userd[o] := 'N';
            end;
            for m := 0 to CMPZSUM - 1 do
            begin
              if DCount3 <= Js_G - 1 then
              begin
                // 取当前最大序号
                maxindex := getIndexForArrayMax(S3, S3Userd);
                if maxindex = -1 then
                begin
                  break;
                end
                else
                begin
                  S3Userd[maxindex] := 'Y';
                  // 第3次序算法又不同了
                  D3[maxindex] := S3[maxindex];
                  S3[maxindex] := S3[maxindex] - S3[maxindex];
                  DCount3 := DCount3 + 1;
                end;
              end;

            end;

            if flag = 0 then
            begin
              // 第三轮做个校验，不理想则结果不用显示
              for o := 0 to CMPZSUM - 1 do
              begin
                S3Userd[o] := 'N';
              end;
              maxindex := getIndexForArrayMax(D3, S3Userd);
              minindex := getIndexForArrayMin(D3, S3Userd);
              if D3[minindex] > 0 then
              begin
                if (D3[maxindex] div D3[minindex] >= 5) or
                  (D3[maxindex] mod D3[minindex] <> 0) then
                begin
                  flag := 1;
                end
                else
                begin
                  DCount := 0;
                  if (D3[maxindex] mod D3[minindex] = 0) then
                  begin
                    for o := 0 to CMPZSUM - 1 do
                    begin
                      DCount := DCount + (D3[o] div D3[minindex]);
                    end;
                    if DCount > Js_G then
                    begin
                      flag := 1;
                    end;
                    if flag = 0 then
                    begin
                      flag := getMfForArray(DCount, D3, C, Role3[i]);
                      if flag <> 0 then
                      begin
                        if (Role3[i] = 200) or (Role3[i] = 100) then
                        begin
                          begin
                            flag := getMfForArray(DCount, D3, C,
                              Role3[i] div 2);
                          end;
                          if flag <> 0 then
                          begin
                            flag := getMfForArray(DCount, D3, C,
                              Role3[i] div 4);
                          end;
                        end;

                      end;

                    end;
                  end;
                end
              end;
            end;
          end;

          if flag = 0 then
          begin
            DCount3 := CalDcountOther(D3);
            js := CalJsOther(D3);
            if DCount3 > Js_G then
            begin
              flag := 1;
            end
            else
            begin
              flag := getMfForArray1(DCount3, D3, C, js);
            end;
          end;
        end;

        if flag = 0 then
        begin

          // 最后在校验下是否全部分配掉了
          Sum1 := 0;
          Sum2 := 0;
          SumD1 := 0;
          SumD2 := 0;
          SumD3 := 0;
          // self.RzEdit1.Text := self.RzEdit1.Text + '<<<' + inttostr(i);
          StringList_S := TStringList.Create;
          StringList_D1 := TStringList.Create;
          StringList_D2 := TStringList.Create;
          StringList_D3 := TStringList.Create;

          for o := 0 to CMPZSUM - 1 do
          begin
            StringList_S.Add(IntToStr(S[o]));
            StringList_D1.Add(IntToStr(D1[o]));
            StringList_D2.Add(IntToStr(D2[o]));
            StringList_D3.Add(IntToStr(D3[o]));
            Sum1 := Sum1 + S[o];
            Sum2 := Sum2 + D1[o] + D2[o] + D3[o];
            SumD1 := SumD1 + D1[o];
            SumD2 := SumD2 + D2[o];
            SumD3 := SumD3 + D3[o];
          end;
          if Sum1 = Sum2 then
          begin
            // 重新计算每个Dcount

            CalDcount1 := CalDcount(StringList_D1);
            CalDCount2 := CalDcount(StringList_D2);
            CalDCount3 := CalDcount(StringList_D3);

            CalDCountSum := CalDcount1 + CalDCount2 + CalDCount3;

            StringList_S.Add(IntToStr(CalDCountSum));
            StringList_D1.Add(IntToStr(CalDcount1));
            StringList_D2.Add(IntToStr(CalDCount2));
            StringList_D3.Add(IntToStr(CalDCount3));
            StringList_D1.Add(IntToStr(SumD1));
            StringList_D2.Add(IntToStr(SumD2));
            StringList_D3.Add(IntToStr(SumD3));
            math75Flag := true;

            if CalDcount1 > 1 then
            begin
              if (SumD1 / CalDcount1 > 75) then
              begin

                if (CalDcount1 >= 3) then
                begin
                  math75Flag := true;
                end
                else
                begin
                  math75Flag := false;
                end;

              end;
            end;

            if math75Flag then
            begin
              if CalDCount2 > 1 then
              begin
                if (SumD2 / CalDCount2 > 75) then
                begin
                  if (CalDCount2 >= 3) then
                  begin
                    math75Flag := true;
                  end
                  else
                  begin
                    math75Flag := false;
                  end;
                end;
              end;
            end;

            if math75Flag then
            begin
              if CalDCount3 > 1 then
              begin
                if (SumD3 / CalDCount3 > 75) then
                begin
                  if (CalDCount3 >= 3) then
                  begin
                    math75Flag := true;
                  end
                  else
                  begin
                    math75Flag := false;
                  end;
                end;
              end;
            end;

            // 这里对于重复数据的判断是否多余，还需要做个验证
            if math75Flag = true then
            begin
              AddRoleFlag1 := false;
              AddRoleFlag2 := false;
              // 加入判断1 是否满足最大投缸量
              // 加入判断2 如果有规则1369，则需要计算
              if (SumD1 * G_YJL < G_MaxTg) and (SumD2 * G_YJL < G_MaxTg) and
                (SumD3 * G_YJL < G_MaxTg) then
              begin
                AddRoleFlag1 := true;
              end;

              if G_Gz1 = '369排' then
              begin
                if ((CalDcount1 = 0) or (CalDcount1 = 1) or
                  ((CalDcount1 mod 3) = 0)) and
                  ((CalDCount2 = 0) or (CalDCount2 = 1) or
                  ((CalDCount2 mod 3) = 0)) and
                  ((CalDCount3 = 0) or (CalDCount3 = 1) or
                  ((CalDCount3 mod 3) = 0)) then
                begin
                  AddRoleFlag2 := true;
                end;
              end
              else
              begin
                if ((CalDcount1 = 0) or (CalDcount1 > 1)) and
                  ((CalDCount2 = 0) or (CalDCount2 > 1)) and
                  ((CalDCount3 = 0) or (CalDCount3 > 1)) then
                begin
                  AddRoleFlag2 := true;
                end
                else
                begin
                  AddRoleFlag2 := false;
                end;
              end;

              if AddRoleFlag1 and AddRoleFlag2 then
              begin

                CStringList_S := TStringList.Create;
                CStringList_D1 := TStringList.Create;
                CStringList_D2 := TStringList.Create;
                CStringList_D3 := TStringList.Create;

                for o := 0 to StringList_S.Count - 1 do
                begin
                  CStringList_S.Add(StringList_S.Strings[o]);
                end;

                for o := 0 to StringList_D1.Count - 1 do
                begin
                  CStringList_D1.Add(StringList_D1.Strings[o]);
                end;

                for o := 0 to StringList_D2.Count - 1 do
                begin
                  CStringList_D2.Add(StringList_D2.Strings[o]);
                end;

                for o := 0 to StringList_D3.Count - 1 do
                begin
                  CStringList_D3.Add(StringList_D3.Strings[o]);
                end;

                List.Add(CStringList_S);
                List.Add(CStringList_D1);
                List.Add(CStringList_D2);
                List.Add(CStringList_D3);

              end;
            end;

          end;
          FreeAndNil(StringList_S);
          FreeAndNil(StringList_D1);
          FreeAndNil(StringList_D2);
          FreeAndNil(StringList_D3);
        end;
      end;

    end;
  end;

  // 这里增加一种平均算法，对于原来按照最大值匹配计算的补充，对所有待处理的层数进行最平均的分配，如果2刀可以就2刀，如果2刀不行就3刀

  // 判断是否要做平均计算
  AveFlag := true;

  for k := 0 to CMPZSUM - 1 do
  begin
    if S[k] > 0 then
    begin
      if S[k] < 100 then
      begin
        AveFlag := false;
        break;
      end
      else
      begin
        S1[k] := S[k];
        D1[k] := 0;
      end;
    end
    else
    begin
      S1[k] := S[k];
      D1[k] := 0;
    end;
  end;

  if AveFlag then
  begin
    if (S1[0] + S1[1] + S1[2] + S1[3] + S1[4] + S1[5] + S1[6] + S1[7] + S1[8] +
      S1[9] + S1[10] + S1[11] + S1[12]) < 0 then
    begin
      AveFlag := false;
    end;
  end;

  // 开始正式进行平均值计算
  if AveFlag then
  begin
    js := CalJsOther(S1);
    for k := 0 to CMPZSUM - 1 do
    begin
      if S1[k] > 0 then
      begin
        S1[k] := S1[k] - js;
        D1[k] := js;
      end;
    end;

    DCount1 := CalDcountOther(D1);
    flag := getMfForArray(DCount1, D1, C, js);

    if DCount1 > StrToInt(G_Js) then
    begin
      flag := 1;
    end;

    // 对于第二轮算法调整，直接减掉第2轮为止
    if flag = 0 then
    begin
      // 第二轮
      for k := 0 to CMPZSUM - 1 do
      begin
        S2[k] := S1[k];
        D2[k] := 0;
      end;

      for k := 0 to CMPZSUM - 1 do
      begin
        if S2[k] > 0 then
        begin
          D2[k] := S2[k];
          S2[k] := S2[k] - S2[k];
        end;
      end;
    end
    else
    begin
      for k := 0 to CMPZSUM - 1 do
      begin
        S2[k] := 0;
        D2[k] := 0;
      end;
    end;

    DCount2 := CalDcountOther(D2);
    flag := getMfForArray(DCount2, D2, C, js);

    if (DCount2 > StrToInt(G_Js)) or (DCount2 = 0) then
    begin
      flag := 1;
    end;

    if flag = 0 then
    begin
      // 第三轮
      for k := 0 to CMPZSUM - 1 do
      begin
        S3[k] := S2[k];
        D3[k] := 0;
      end;

      if (S3[0] + S3[1] + S3[2] + S3[3] + S3[4] + S3[5] + S3[6] + S3[7] + S3[8]
        + S3[9] + S3[10] + S3[11] + S3[12]) > 0 then
      begin
        for k := 0 to CMPZSUM - 1 do
        begin
          if S3[k] > 0 then
          begin
            S3[k] := S3[k] - js;
            D3[k] := js;
          end;
        end;
      end
      else
      begin
        for k := 0 to CMPZSUM - 1 do
        begin
          S3[k] := 0;
          D3[k] := 0;
        end;

      end;
      DCount3 := CalDcountOther(D3);
      flag := getMfForArray(DCount3, D3, C, js);

      // 修改这个逻辑，不知道当时怎么考虑的(DCount3 = 0)

      // if (DCount3 > StrToInt(self.RzEdit2.Text)) or (DCount3 = 0) then
      if (DCount3 > StrToInt(G_Js)) then
      begin
        flag := 1;
      end;

    end;

    if flag = 0 then
    begin

      // 最后在校验下是否全部分配掉了
      Sum1 := 0;
      Sum2 := 0;
      SumD1 := 0;
      SumD2 := 0;
      SumD3 := 0;

      StringList_S := TStringList.Create;
      StringList_D1 := TStringList.Create;
      StringList_D2 := TStringList.Create;
      StringList_D3 := TStringList.Create;

      for o := 0 to CMPZSUM - 1 do
      begin
        StringList_S.Add(IntToStr(S[o]));
        StringList_D1.Add(IntToStr(D1[o]));
        StringList_D2.Add(IntToStr(D2[o]));
        StringList_D3.Add(IntToStr(D3[o]));
        Sum1 := Sum1 + S[o];
        Sum2 := Sum2 + D1[o] + D2[o] + D3[o];
        SumD1 := SumD1 + D1[o];
        SumD2 := SumD2 + D2[o];
        SumD3 := SumD3 + D3[o];
      end;
      if Sum1 = Sum2 then
      begin
        // 重新计算每个Dcount

        CalDcount1 := CalDcount(StringList_D1);
        CalDCount2 := CalDcount(StringList_D2);
        CalDCount3 := CalDcount(StringList_D3);

        CalDCountSum := CalDcount1 + CalDCount2 + CalDCount3;

        StringList_S.Add(IntToStr(CalDCountSum));
        StringList_D1.Add(IntToStr(CalDcount1));
        StringList_D2.Add(IntToStr(CalDCount2));
        StringList_D3.Add(IntToStr(CalDCount3));
        StringList_D1.Add(IntToStr(SumD1));
        StringList_D2.Add(IntToStr(SumD2));
        StringList_D3.Add(IntToStr(SumD3));
        math75Flag := true;

        if CalDcount1 > 0 then
        begin
          if (SumD1 / CalDcount1 > 75) then
          begin

            if (CalDcount1 >= 3) then
            begin
              math75Flag := true;
            end
            else
            begin
              math75Flag := false;
            end;

          end;
        end;

        if math75Flag then
        begin
          if CalDCount2 > 0 then
          begin
            if (SumD2 / CalDCount2 > 75) then
            begin
              if (CalDCount2 >= 3) then
              begin
                math75Flag := true;
              end
              else
              begin
                math75Flag := false;
              end;
            end;
          end;
        end;

        if math75Flag then
        begin
          if CalDCount3 > 0 then
          begin
            if (SumD3 / CalDCount3 > 75) then
            begin
              if (CalDCount3 >= 3) then
              begin
                math75Flag := true;
              end
              else
              begin
                math75Flag := false;
              end;
            end;
          end;
        end;

        // 这里对于重复数据的判断是否多余，还需要做个验证
        if math75Flag = true then
        begin

          CStringList_S := TStringList.Create;
          CStringList_D1 := TStringList.Create;
          CStringList_D2 := TStringList.Create;
          CStringList_D3 := TStringList.Create;

          for o := 0 to StringList_S.Count - 1 do
          begin
            CStringList_S.Add(StringList_S.Strings[o]);
          end;

          for o := 0 to StringList_D1.Count - 1 do
          begin
            CStringList_D1.Add(StringList_D1.Strings[o]);
          end;

          for o := 0 to StringList_D2.Count - 1 do
          begin
            CStringList_D2.Add(StringList_D2.Strings[o]);
          end;

          for o := 0 to StringList_D3.Count - 1 do
          begin
            CStringList_D3.Add(StringList_D3.Strings[o]);
          end;

          List.Add(CStringList_S);
          List.Add(CStringList_D1);
          List.Add(CStringList_D2);
          List.Add(CStringList_D3);

        end;

      end;
      FreeAndNil(StringList_S);
      FreeAndNil(StringList_D1);
      FreeAndNil(StringList_D2);
      FreeAndNil(StringList_D3);
    end;

  end;

  // 重置数据
  setlength(S, 0);
  setlength(S1, 0);
  setlength(S2, 0);
  setlength(S3, 0);
  setlength(D1, 0);
  setlength(D2, 0);
  setlength(D3, 0);
  setlength(S1Userd, 0);
  setlength(S2Userd, 0);
  setlength(S3Userd, 0);
  setlength(C, 0);

  setlength(Role1, 0);
  setlength(Role2, 0);
  setlength(Role3, 0);

  if FlagType = 1 then
  begin
    result := List;
  end
  else
  begin
    // 根据当前list取最好的结果

    ReturnList := FillOnceReturn(List, 2);
    ReturnList_n := TList.Create;
    for i := 0 to ReturnList.Count - 1 do
    begin
      ReturnStringList_n := TStringList(ReturnList.Items[i]);
      ReturnStringList_Copy := TStringList.Create;
      for o := 0 to ReturnStringList_n.Count - 1 do
      begin
        ReturnStringList_Copy.Add(ReturnStringList_n.Strings[o]);
      end;
      ReturnList_n.Add(ReturnStringList_Copy);
    end;

    for i := 0 to List.Count - 1 do
    begin
      StringList_n := TStringList(List.Items[i]);
      StringList_n.Free;
    end;

    FreeAndNil(List);

    result := ReturnList_n;
  end;

end;

function TThreadPdCal.CalOnceTenSingle(changei: Integer;
  SIn: array of Integer): TList;
var
  i, k, m, n, o, p: Integer;
  S: Array of Integer;
  S1: Array of Integer;
  S1Userd: Array of String;
  S2: Array of Integer;
  S2Userd: Array of String;
  S3: Array of Integer;
  S3Userd: Array of String;
  S4: Array of Integer;
  S4Userd: Array of String;
  S5: Array of Integer;
  S5Userd: Array of String;
  S6: Array of Integer;
  S6Userd: Array of String;
  S7: Array of Integer;
  S7Userd: Array of String;
  S8: Array of Integer;
  S8Userd: Array of String;
  S9: Array of Integer;
  S9Userd: Array of String;
  S10: Array of Integer;
  S10Userd: Array of String;

  D1: Array of Integer;
  D2: Array of Integer;
  D3: Array of Integer;
  D4: Array of Integer;
  D5: Array of Integer;
  D6: Array of Integer;
  D7: Array of Integer;
  D8: Array of Integer;
  D9: Array of Integer;
  D10: Array of Integer;
  DCount, DCount1, DCount2, DCount3, DCount4, DCount5, DCount6, DCount7,
    DCount8, DCount9, DCount10: Integer;
  maxindex, minindex: Integer;
  mtop: Integer;
  Role1: Array of Integer;
  Role2: Array of Integer;
  Role3: Array of Integer;
  Role4: Array of Integer;
  Role5: Array of Integer;
  Role6: Array of Integer;
  Role7: Array of Integer;
  Role8: Array of Integer;
  Role9: Array of Integer;
  Role10: Array of Integer;
  flag: Integer;
  C: Array of Integer;
  List, ListFormat, ListFormatShow: TList;
  StringList_S, StringList_D, StringList_D1, StringList_D2, StringList_D3,
    StringList_D4, StringList_D5, StringList_D6, StringList_D7, StringList_D8,
    StringList_D9, StringList_D10: TStringList;
  CStringList_S, CStringList_D, CStringList_D1, CStringList_D2, CStringList_D3,
    CStringList_D4, CStringList_D5, CStringList_D6, CStringList_D7,
    CStringList_D8, CStringList_D9, CStringList_D10: TStringList;
  Sum1: Integer;
  Sum2: Integer;
  SumD1, SumD2, SumD3, SumD4, SumD5, SumD6, SumD7, SumD8, SumD9,
    SumD10: Integer;
  Js_G: Integer;
  SumStringList1, SumStringList2: TStringList;
  DCountMin, DCountMinIndex, DCountMinIndex1: Integer;
  CalDCountSum, CalDcount1, CalDCount2, CalDCount3, CalDCount4, CalDCount5,
    CalDCount6, CalDCount7, CalDCount8, CalDCount9, CalDCount10: Integer;
  ItemIndex: Array of Integer;
  ItemCount: Integer;
  UsedIndex: Array of Integer;
  math75Flag: Boolean;
  r1, r2, r3, r4, r5, r6, r7, r8, r9, r10: Integer;
  rcount: Integer;
  ListFilter1, ListFilter2, ListFilter3, ListFilter4, ListFilter5, ListFilter6,
    ListFilter7, ListFilter8, ListFilter9, ListFilter10, ListFilterAll: TList;
  js: Integer;
  DsMin: Integer;
  DsItem: Integer;
  q: Integer;
  AveFlag: Boolean;
  maxindex1, maxindex2: Integer;
  maxindexcompare: Integer;
  Step1List: TStringList;
  AddRoleFlag1, AddRoleFlag2: Boolean;
  List1_n, List2_n, List3_n, List4_n, ListAll_n, ReturnList_n: TList;
  StringList_n: TStringList;
  StringList_n1: TStringList;
  CStringList_n: TStringList;
  D6StringList, D7StringList, D8StringList, D9StringList,
    D10StringList: TStringList;
  i_n, j_n: Integer;
  ThreeFlag: Boolean;
  r: Integer;
  ListAllString_n: TStringList;
  SMin: Integer;
  S1Sum: Integer;
  CalDcount1_1, CalDcount2_1, CalDcount3_1, CalDcount4_1, CalDcount5_1: Integer;
  CStringList: TStringList;
begin

  Js_G := StrToInt(G_Js);
  FzMin_G := StrToFloat(G_Fz_B);
  FzMax_G := StrToFloat(G_Fz_E);

  // 读取数据
  setlength(S, CMPZSUM);
  setlength(S1, CMPZSUM);
  setlength(S2, CMPZSUM);
  setlength(S3, CMPZSUM);
  setlength(S4, CMPZSUM);
  setlength(S5, CMPZSUM);
  setlength(S6, CMPZSUM);
  setlength(S7, CMPZSUM);
  setlength(S8, CMPZSUM);
  setlength(S9, CMPZSUM);
  setlength(S10, CMPZSUM);
  setlength(D1, CMPZSUM);
  setlength(D2, CMPZSUM);
  setlength(D3, CMPZSUM);
  setlength(D4, CMPZSUM);
  setlength(D5, CMPZSUM);
  setlength(D6, CMPZSUM);
  setlength(D7, CMPZSUM);
  setlength(D8, CMPZSUM);
  setlength(D9, CMPZSUM);
  setlength(D10, CMPZSUM);
  setlength(S1Userd, CMPZSUM);
  setlength(S2Userd, CMPZSUM);
  setlength(S3Userd, CMPZSUM);
  setlength(S4Userd, CMPZSUM);
  setlength(S5Userd, CMPZSUM);
  setlength(S6Userd, CMPZSUM);
  setlength(S7Userd, CMPZSUM);
  setlength(S8Userd, CMPZSUM);
  setlength(S9Userd, CMPZSUM);
  setlength(S10Userd, CMPZSUM);

  setlength(C, CMPZSUM);

  S[0] := SIn[0];
  S[1] := SIn[1];
  S[2] := SIn[2];
  S[3] := SIn[3];
  S[4] := SIn[4];
  S[5] := SIn[5];
  S[6] := SIn[6];
  S[7] := SIn[7];
  S[8] := SIn[8];
  S[9] := SIn[9];
  S[10] := SIn[10];
  S[11] := SIn[11];
  S[12] := SIn[12];

  // 根据数据库中配置取值
  CStringList := GetCmFzFromCmType(G_CmTypenum);
  for i := 0 to CStringList.Count - 1 do
  begin
    C[i] := StrToInt(CStringList.Strings[i]);
  end;

  // C[0] := 1;
  // C[1] := 2;
  // C[2] := 3;
  // C[3] := 4;
  // C[4] := 5;
  // C[5] := 6;
  // C[6] := 7;
  // C[7] := 8;
  // C[8] := 9;
  // C[9] := 10;
  // C[10] := 11;
  // C[11] := 12;
  // C[12] := 13;

  setlength(Role1, CengShu * CengShu * CengShu * CengShu * CengShu);
  setlength(Role2, CengShu * CengShu * CengShu * CengShu * CengShu);
  setlength(Role3, CengShu * CengShu * CengShu * CengShu * CengShu);
  setlength(Role4, CengShu * CengShu * CengShu * CengShu * CengShu);
  setlength(Role5, CengShu * CengShu * CengShu * CengShu * CengShu);
  setlength(Role6, CengShu * CengShu * CengShu * CengShu * CengShu);
  setlength(Role7, CengShu * CengShu * CengShu * CengShu * CengShu);
  setlength(Role8, CengShu * CengShu * CengShu * CengShu * CengShu);
  setlength(Role9, CengShu * CengShu * CengShu * CengShu * CengShu);
  setlength(Role10, CengShu * CengShu * CengShu * CengShu * CengShu);

  rcount := 0;
  for r1 := 0 to CengShu - 1 do
  begin
    for r2 := 0 to CengShu - 1 do
    begin
      for r3 := 0 to CengShu - 1 do
      begin
        for r4 := 0 to CengShu - 1 do
        begin
          for r5 := 0 to CengShu - 1 do
          begin
            // for r6 := 0 to CengShu - 1 do
            // begin
            // for r7 := 0 to CengShu - 1 do
            // begin
            // for r8 := 0 to CengShu - 1 do
            // begin
            // for r9 := 0 to CengShu - 1 do
            // begin
            // for r10 := 0 to CengShu - 1 do
            // begin
            Role1[rcount] := (r1 + 1) * 25;
            Role2[rcount] := (r2 + 1) * 25;
            Role3[rcount] := (r3 + 1) * 25;
            Role4[rcount] := (r4 + 1) * 25;
            Role5[rcount] := (r5 + 1) * 25;
            // Role6[rcount] := (r1 + 1) * 25;
            // Role7[rcount] := (r2 + 1) * 25;
            // Role8[rcount] := (r3 + 1) * 25;
            // Role9[rcount] := (r4 + 1) * 25;
            // Role10[rcount] := (r5 + 1) * 25;
            rcount := rcount + 1;
            // self.Memo1.Lines.Add(inttostr(rcount));
            // end;
            // end;
            // end;
            // end;
            // end;

          end;
        end;

      end;
    end;
  end;

  List := TList.Create;

  SMin := S[self.getIndexForArrayMin1(S)];

  // 加载基础配置信息，计算平均分值，不符合就淘汰此方案
  // 已25为基数，200最大，递减，进行每个尺码的取整

  flag := 0;

  // G_Js := '8';
  // 最大件数从7开始，因为是10层的排刀，在改回到从0开始
  if StrToInt(G_Js) >= 0 then
  begin
    for q := 6 to StrToInt(G_Js) - 1 do
    // for q := 6 to 6 do
    begin
      Js_G := q + 1;

      for i := CengShu * CengShu * CengShu * CengShu * CengShu - 1 downto 0 do
      begin

        // 第一轮
        for k := 0 to CMPZSUM - 1 do
        begin
          S1[k] := S[k];
          D1[k] := 0;
        end;
        DCount1 := 0;
        for o := 0 to CMPZSUM - 1 do
        begin
          S1Userd[o] := 'N';
        end;

        // 这里做分支，处理多种情况，最终的结果都记录在list中

        // 流程1
        maxindexcompare := 0;
        S1Sum := 0;
        for m := 0 to CMPZSUM - 1 do
        begin
          if DCount1 <= Js_G - 1 then
          begin
            // 取当前最大序号
            if (changei = 4) and (self.getNumberForArrayMax(S1Userd) = 1) then
            begin
              maxindex1 := getIndexForArrayMax2(S1, S1Userd);
              maxindex2 := getIndexForArrayMax3(S1, S1Userd);
            end
            else
            begin
              maxindex1 := getIndexForArrayMax(S1, S1Userd);
              maxindex2 := getIndexForArrayMax1(S1, S1Userd);
            end;

            if maxindex1 = maxindex2 then
            begin
              maxindex := maxindex1;
              maxindexcompare := 0;
            end
            else
            begin
              if (m mod 2 = 0) then
              begin
                maxindex := maxindex1;
                maxindexcompare := 0;
              end
              else
              begin
                maxindexcompare := 1;
              end;
            end;

            if maxindex = -1 then
            begin
              break;
            end
            else
            begin
              if maxindexcompare = 1 then
              begin
                S1Userd[maxindex2] := 'Y';
                if S1[maxindex2] >= Role1[i] then
                begin
                  mtop := S1[maxindex2] div Role1[i];
                  for n := mtop downto 1 do
                  begin
                    if ((changei = 1) and ((S1[maxindex2] - Role1[i] * n)
                      mod (Role2[i]) = 0)) or
                      ((changei = 1) and ((S1[maxindex2] - Role1[i] * n)
                      mod (Role3[i]) = 0)) or
                      ((changei = 1) and ((S1[maxindex2] - Role1[i] * n) mod (1)
                      = 0)) or ((changei = 2) and
                      ((S1[maxindex2] - Role1[i] * n) mod (Role2[i] + Role3[i])
                      = 0)) or ((changei = 3) and
                      ((S1[maxindex2] - Role1[i] * n) = 0)) or
                      ((changei = 4) and ((S1[maxindex2] - Role1[i] * n)
                      mod (Role2[i]) = 0)) or
                      ((changei = 4) and ((S1[maxindex2] - Role1[i] * n)
                      mod (Role3[i]) = 0)) or
                      ((changei = 4) and ((S1[maxindex2] - Role1[i] * n) mod (1)
                      = 0)) then
                    begin
                      if DCount1 + n <= Js_G then
                      begin
                        if (S1Sum + Role1[i] * n) * G_YJL < G_MaxTg then
                        begin
                          S1[maxindex2] := S1[maxindex2] - Role1[i] * n;
                          D1[maxindex2] := Role1[i] * n;
                          DCount1 := DCount1 + n;
                          S1Sum := S1Sum + Role1[i] * n;
                          break;
                        end;
                      end;
                    end;
                  end;
                end;
              end
              else
              begin

                S1Userd[maxindex] := 'Y';
                if S1[maxindex] >= Role1[i] then
                begin
                  mtop := S1[maxindex] div Role1[i];
                  for n := mtop downto 1 do
                  begin
                    if ((changei = 1) and ((S1[maxindex] - Role1[i] * n)
                      mod (Role2[i]) = 0)) or
                      ((changei = 1) and ((S1[maxindex] - Role1[i] * n)
                      mod (Role3[i]) = 0)) or
                      ((changei = 1) and ((S1[maxindex] - Role1[i] * n) mod (1)
                      = 0)) or ((changei = 2) and
                      ((S1[maxindex] - Role1[i] * n) mod (Role2[i] + Role3[i])
                      = 0)) or ((changei = 3) and
                      ((S1[maxindex] - Role1[i] * n) = 0)) or
                      ((changei = 4) and ((S1[maxindex] - Role1[i] * n)
                      mod (Role2[i]) = 0)) or
                      ((changei = 4) and ((S1[maxindex] - Role1[i] * n)
                      mod (Role3[i]) = 0)) or
                      ((changei = 4) and ((S1[maxindex] - Role1[i] * n) mod (1)
                      = 0)) then
                    begin
                      if DCount1 + n <= Js_G then
                      begin
                        if (S1Sum + Role1[i] * n) * G_YJL < G_MaxTg then
                        begin
                          S1[maxindex] := S1[maxindex] - Role1[i] * n;
                          D1[maxindex] := Role1[i] * n;
                          DCount1 := DCount1 + n;
                          S1Sum := S1Sum + Role1[i] * n;
                          break;
                        end;
                      end;
                    end;
                  end;
                end;
              end;
            end;
          end;
        end;

        // 第一轮计算完毕后，进行门幅的校验
        flag := getMfForArray(DCount1, D1, C, Role1[i]);

        // 如果mf校验未通过，则需要进行校正
        if flag > 0 then
        begin
          if flag = 1 then
          begin
            for k := 0 to CMPZSUM - 1 do
            begin
              if D1[k] > 0 then
              begin
                S1[k] := S1[k] + D1[k];
                D1[k] := 0;
                break;
              end;
            end;
          end;

          if flag = 2 then
          begin
            for k := CMPZSUM - 1 downto 0 do
            begin
              if D1[k] > 0 then
              begin
                S1[k] := S1[k] + D1[k];
                D1[k] := 0;
                break;
              end;
            end;
          end;
          DCount1 := CalDcountOther(D1);
          flag := getMfForArray(DCount1, D1, C, Role1[i]);
        end;

        if flag = 0 then
        begin

          // 第二轮
          for k := 0 to CMPZSUM - 1 do
          begin
            S2[k] := S1[k];
            D2[k] := 0;
          end;

          if (S2[0] + S2[1] + S2[2] + S2[3] + S2[4] + S2[5] + S2[6] + S2[7] +
            S2[8] + S2[9] + S2[10] + S2[11] + S2[12]) > 0 then
          begin
            DCount2 := 0;
            for o := 0 to CMPZSUM - 1 do
            begin
              S2Userd[o] := 'N';
            end;
            for m := 0 to CMPZSUM - 1 do
            begin
              if DCount2 <= Js_G - 1 then
              begin
                // 取当前最大序号
                maxindex := getIndexForArrayMax(S2, S2Userd);
                if maxindex = -1 then
                begin
                  break;
                end
                else
                begin
                  S2Userd[maxindex] := 'Y';
                  if S2[maxindex] >= Role2[i] then
                  begin
                    mtop := S2[maxindex] div (Role2[i]);
                    for n := mtop downto 1 do
                    begin
                      if (Role3[i] = 200) or (Role3[i] = 100) or
                        (Role3[i] = 150) or (Role3[i] = 125) or (Role3[i] = 175)
                      then
                      begin
                        if ((S2[maxindex] - Role2[i] * n) mod (Role3[i]) = 0) or
                          ((S2[maxindex] - Role2[i] * n) mod (Role3[i] div 2)
                          = 0) or ((S2[maxindex] - Role2[i] * n)
                          mod (Role3[i] div 4) = 0) or
                          ((S2[maxindex] - Role2[i] * n) = 0) or
                          ((S2[maxindex] - Role2[i] * n) mod (SMin) = 0) then
                        begin
                          if DCount2 + n <= Js_G then
                          begin
                            S2[maxindex] := S2[maxindex] - Role2[i] * n;
                            D2[maxindex] := Role2[i] * n;
                            DCount2 := DCount2 + n;
                            break;
                          end;
                        end;
                      end
                      else
                      begin
                        if ((S2[maxindex] - Role2[i] * n) mod (Role3[i]) = 0) or
                          ((S2[maxindex] - Role2[i] * n) = 0) or
                          ((S2[maxindex] - Role2[i] * n)
                          mod (Role3[i] div 2) = 0) then
                        begin
                          if DCount2 + n <= Js_G then
                          begin
                            S2[maxindex] := S2[maxindex] - Role2[i] * n;
                            D2[maxindex] := Role2[i] * n;
                            DCount2 := DCount2 + n;
                            break;
                          end;
                        end;
                      end;
                    end;
                  end;
                end;
              end;
            end;

            flag := getMfForArray(DCount2, D2, C, Role2[i]);

            // 如果mf校验未通过，则需要进行校正
            if flag > 0 then
            begin
              if flag = 1 then
              begin
                for k := 0 to CMPZSUM - 1 do
                begin
                  if D2[k] > 0 then
                  begin
                    S2[k] := S2[k] + D2[k];
                    D2[k] := 0;
                    break;
                  end;
                end;
              end;

              if flag = 2 then
              begin
                for k := CMPZSUM - 1 downto 0 do
                begin
                  if D2[k] > 0 then
                  begin
                    S2[k] := S2[k] + D2[k];
                    D2[k] := 0;
                    break;
                  end;
                end;
              end;
              DCount2 := CalDcountOther(D2);
              flag := getMfForArray(DCount2, D2, C, Role2[i]);
            end;
          end;

        end;

        if flag = 0 then
        begin

          // 第三轮
          for k := 0 to CMPZSUM - 1 do
          begin
            S3[k] := S2[k];
            D3[k] := 0;
          end;

          if (S3[0] + S3[1] + S3[2] + S3[3] + S3[4] + S3[5] + S3[6] + S3[7] +
            S3[8] + S3[9] + S3[10] + S3[11] + S3[12]) > 0 then
          begin
            DCount3 := 0;
            for o := 0 to CMPZSUM - 1 do
            begin
              S3Userd[o] := 'N';
            end;
            for m := 0 to CMPZSUM - 1 do
            begin
              if DCount3 <= Js_G - 1 then
              begin
                // 取当前最大序号
                maxindex := getIndexForArrayMax(S3, S3Userd);
                if maxindex = -1 then
                begin
                  break;
                end
                else
                begin
                  S3Userd[maxindex] := 'Y';
                  if S3[maxindex] >= Role3[i] then
                  begin
                    mtop := S3[maxindex] div (Role3[i]);
                    for n := mtop downto 1 do
                    begin
                      if ((S3[maxindex] - Role3[i] * n) mod (Role3[i]) = 0) or
                        ((S3[maxindex] - Role3[i] * n) mod (Role3[i] div 2) = 0)
                        or ((S3[maxindex] - Role3[i] * n) = 0) or
                        ((S3[maxindex] - Role3[i] * n) mod (SMin) = 0) then
                      begin
                        if DCount3 + n <= Js_G then
                        begin
                          S3[maxindex] := S3[maxindex] - Role3[i] * n;
                          D3[maxindex] := Role3[i] * n;
                          DCount3 := DCount3 + n;
                          break;
                        end;
                      end;
                    end;
                  end;
                end;
              end;
            end;

            flag := getMfForArray(DCount3, D3, C, Role3[i]);

            // 如果mf校验未通过，则需要进行校正
            if flag > 0 then
            begin
              if flag = 1 then
              begin
                for k := 0 to CMPZSUM - 1 do
                begin
                  if D3[k] > 0 then
                  begin
                    S3[k] := S3[k] + D3[k];
                    D3[k] := 0;
                    break;
                  end;
                end;
              end;

              if flag = 2 then
              begin
                for k := CMPZSUM - 1 downto 0 do
                begin
                  if D3[k] > 0 then
                  begin
                    S3[k] := S3[k] + D3[k];
                    D3[k] := 0;
                    break;
                  end;
                end;
              end;
              DCount3 := CalDcountOther(D3);
              flag := getMfForArray(DCount3, D3, C, Role3[i]);
              if flag <> 0 then
              begin
                if (Role3[i] = 200) or (Role3[i] = 100) then
                begin
                  begin
                    flag := getMfForArray(DCount, D3, C, Role3[i] div 2);
                  end;
                  if flag <> 0 then
                  begin
                    flag := getMfForArray(DCount, D3, C, Role3[i] div 4);
                  end;
                end;
              end;
            end;
          end;

        end;

        if flag = 0 then
        begin

          // 第4轮
          for k := 0 to CMPZSUM - 1 do
          begin
            S4[k] := S3[k];
            D4[k] := 0;
          end;

          if (S4[0] + S4[1] + S4[2] + S4[3] + S4[4] + S4[5] + S4[6] + S4[7] +
            S4[8] + S4[9] + S4[10] + S4[11] + S4[12]) > 0 then
          begin
            DCount4 := 0;
            for o := 0 to CMPZSUM - 1 do
            begin
              S4Userd[o] := 'N';
            end;
            for m := 0 to CMPZSUM - 1 do
            begin
              if DCount4 <= Js_G - 1 then
              begin
                // 取当前最大序号
                maxindex := getIndexForArrayMax(S4, S4Userd);
                if maxindex = -1 then
                begin
                  break;
                end
                else
                begin
                  S4Userd[maxindex] := 'Y';
                  if S4[maxindex] >= Role4[i] then
                  begin
                    mtop := S4[maxindex] div (Role4[i]);
                    for n := mtop downto 1 do
                    begin
                      if ((S4[maxindex] - Role4[i] * n) mod (Role3[i]) = 0) or
                        ((S4[maxindex] - Role4[i] * n) = 0) then
                      begin
                        if DCount4 + n <= Js_G then
                        begin
                          S4[maxindex] := S4[maxindex] - Role4[i] * n;
                          D4[maxindex] := Role4[i] * n;
                          DCount4 := DCount4 + n;
                          break;
                        end;
                      end;
                    end;
                  end;
                end;
              end;
            end;

            flag := getMfForArray(DCount4, D4, C, Role4[i]);

            // 如果mf校验未通过，则需要进行校正
            if flag > 0 then
            begin
              if flag = 1 then
              begin
                for k := 0 to CMPZSUM - 1 do
                begin
                  if D4[k] > 0 then
                  begin
                    S4[k] := S4[k] + D4[k];
                    D4[k] := 0;
                    break;
                  end;
                end;
              end;

              if flag = 2 then
              begin
                for k := CMPZSUM - 1 downto 0 do
                begin
                  if D4[k] > 0 then
                  begin
                    S4[k] := S4[k] + D4[k];
                    D4[k] := 0;
                    break;
                  end;
                end;
              end;
              DCount4 := CalDcountOther(D4);
              flag := getMfForArray(DCount4, D4, C, Role4[i]);
            end;
          end;

        end;

        if flag = 0 then
        begin

          // 第5轮
          for k := 0 to CMPZSUM - 1 do
          begin
            S5[k] := S4[k];
            D5[k] := 0;
          end;

          if (S5[0] + S5[1] + S5[2] + S5[3] + S5[4] + S5[5] + S5[6] + S5[7] +
            S5[8] + S5[9] + S5[10] + S5[11] + S5[12]) > 0 then
          begin
            DCount5 := 0;
            for o := 0 to CMPZSUM - 1 do
            begin
              S5Userd[o] := 'N';
            end;
            for m := 0 to CMPZSUM - 1 do
            begin
              if DCount5 <= Js_G - 1 then
              begin
                // 取当前最大序号
                maxindex := getIndexForArrayMax(S5, S5Userd);
                if maxindex = -1 then
                begin
                  break;
                end
                else
                begin
                  S5Userd[maxindex] := 'Y';
                  if S5[maxindex] >= Role5[i] then
                  begin
                    mtop := S5[maxindex] div (Role5[i]);
                    for n := mtop downto 1 do
                    begin
                      if ((S5[maxindex] - Role5[i] * n) mod (Role4[i]) = 0) or
                        ((S5[maxindex] - Role5[i] * n) = 0) then
                      begin
                        if DCount5 + n <= Js_G then
                        begin
                          S5[maxindex] := S5[maxindex] - Role5[i] * n;
                          D5[maxindex] := Role5[i] * n;
                          DCount5 := DCount5 + n;
                          break;
                        end;
                      end;
                    end;
                  end;
                end;
              end;
            end;

            flag := getMfForArray(DCount5, D5, C, Role5[i]);

            // 如果mf校验未通过，则需要进行校正
            if flag > 0 then
            begin
              if flag = 1 then
              begin
                for k := 0 to CMPZSUM - 1 do
                begin
                  if D5[k] > 0 then
                  begin
                    S5[k] := S5[k] + D5[k];
                    D5[k] := 0;
                    break;
                  end;
                end;
              end;

              if flag = 2 then
              begin
                for k := CMPZSUM - 1 downto 0 do
                begin
                  if D5[k] > 0 then
                  begin
                    S5[k] := S5[k] + D5[k];
                    D5[k] := 0;
                    break;
                  end;
                end;
              end;
              DCount5 := CalDcountOther(D5);
              flag := getMfForArray(DCount5, D5, C, Role5[i]);
            end;
          end;

        end;

        // 第6轮开始前，需要先进行初步校验，对于不符合部分条件的结果，不用进入后续流程
        // 此规则先取消
        // if G_Gz1 = '369排' then
        // begin
        // CalDcount1_1 := CalDcountOther(D1);
        // CalDcount2_1 := CalDcountOther(D2);
        // CalDcount3_1 := CalDcountOther(D3);
        // CalDcount4_1 := CalDcountOther(D4);
        // CalDcount5_1 := CalDcountOther(D5);
        //
        // if ((CalDcount1_1 = 0) or (CalDcount1_1 = 1) or
        // ((CalDcount1_1 mod 3) = 0)) and
        // ((CalDcount2_1 = 0) or (CalDcount2_1 = 1) or
        // ((CalDcount2_1 mod 3) = 0)) and
        // ((CalDcount3_1 = 0) or (CalDcount3_1 = 1) or
        // ((CalDcount3_1 mod 3) = 0)) and
        // ((CalDcount4_1 = 0) or (CalDcount4_1 = 1) or
        // ((CalDcount4_1 mod 3) = 0)) and
        // ((CalDcount5_1 = 0) or (CalDcount5_1 = 1) or
        // ((CalDcount5_1 mod 3) = 0)) then
        // begin
        // flag := 0;
        // end
        // else
        // begin
        // flag := 1;
        // end;
        // end;

        if flag = 0 then
        begin

          // 第6轮
          for k := 0 to CMPZSUM - 1 do
          begin
            S6[k] := S5[k];
            D6[k] := 0;
          end;

          if S[0] + S[1] + S[2] + S[3] + S[4] + S[5] + S[6] + S[7] + S[8] + S[9]
            + S[10] + S[11] + S[12] <= 7500 then
          begin

            if S[0] + S[1] + S[2] + S[3] + S[4] + S[5] + S[6] + S[7] + S[8] +
              S[9] + S[10] + S[11] + S[12] <= 6500 then
            begin
              // 数量上判断，剩余20%都算合格
              if ((S6[0] + S6[1] + S6[2] + S6[3] + S6[4] + S6[5] + S6[6] + S6[7]
                + S6[8] + S6[9] + S6[10] + S6[11] + S6[12]) > 0) and
                ((S6[0] + S6[1] + S6[2] + S6[3] + S6[4] + S6[5] + S6[6] + S6[7]
                + S6[8] + S6[9] + S6[10] + S6[11] + S6[12]) <=
                (S[0] + S[1] + S[2] + S[3] + S[4] + S[5] + S[6] + S[7] + S[8] +
                S[9] + S[10] + S[11] + S[12]) * 0.07) then
              begin

                ThreeFlag := false;

                // WriteImportLog1('C:\1.txt', IntToStr(i) + '<<<begin1<<<' +
                // format('memory use: %d KB',
                // [GetProcessMemUse(GetCurrentProcessId)]));

                ListAll_n := TList.Create;
                List1_n := CalOnceSingle(1, S6, 2);
                List2_n := CalOnceSingle(2, S6, 2);
                List3_n := CalOnceSingle(3, S6, 2);
                // List4_n := CalOnceSingle(4, S6, 2);

                // WriteImportLog1('C:\1.txt', IntToStr(i) + '<<<end1<<<' +
                // format('memory use: %d KB',
                // [GetProcessMemUse(GetCurrentProcessId)]));

                for i_n := 0 to List1_n.Count - 1 do
                begin
                  StringList_n := TStringList(List1_n.Items[i_n]);
                  ListAll_n.Add(StringList_n);
                end;

                for i_n := 0 to List2_n.Count - 1 do
                begin
                  StringList_n := TStringList(List2_n.Items[i_n]);
                  ListAll_n.Add(StringList_n);
                end;

                for i_n := 0 to List3_n.Count - 1 do
                begin
                  StringList_n := TStringList(List3_n.Items[i_n]);
                  ListAll_n.Add(StringList_n);
                end;

                // for i_n := 0 to List4_n.Count - 1 do
                // begin
                // StringList_n := TStringList(List4_n.Items[i_n]);
                // ListAll_n.Add(StringList_n);
                // end;

                ReturnList_n := FillOnceReturn(ListAll_n, 1);
                if ReturnList_n.Count > 0 then
                begin
                  ThreeFlag := true;
                  D6StringList := TStringList(ReturnList_n.Items[0]);
                  D6[0] := StrToInt(D6StringList.Strings[0]);
                  D6[1] := StrToInt(D6StringList.Strings[1]);
                  D6[2] := StrToInt(D6StringList.Strings[2]);
                  D6[3] := StrToInt(D6StringList.Strings[3]);
                  D6[4] := StrToInt(D6StringList.Strings[4]);
                  D6[5] := StrToInt(D6StringList.Strings[5]);
                  D6[6] := StrToInt(D6StringList.Strings[6]);
                  D6[7] := StrToInt(D6StringList.Strings[7]);
                  D6[8] := StrToInt(D6StringList.Strings[8]);
                  D6[9] := StrToInt(D6StringList.Strings[9]);
                  D6[10] := StrToInt(D6StringList.Strings[10]);
                  D6[11] := StrToInt(D6StringList.Strings[11]);
                  D6[12] := StrToInt(D6StringList.Strings[12]);

                  D7StringList := TStringList(ReturnList_n.Items[1]);
                  D7[0] := StrToInt(D7StringList.Strings[0]);
                  D7[1] := StrToInt(D7StringList.Strings[1]);
                  D7[2] := StrToInt(D7StringList.Strings[2]);
                  D7[3] := StrToInt(D7StringList.Strings[3]);
                  D7[4] := StrToInt(D7StringList.Strings[4]);
                  D7[5] := StrToInt(D7StringList.Strings[5]);
                  D7[6] := StrToInt(D7StringList.Strings[6]);
                  D7[7] := StrToInt(D7StringList.Strings[7]);
                  D7[8] := StrToInt(D7StringList.Strings[8]);
                  D7[9] := StrToInt(D7StringList.Strings[9]);
                  D7[10] := StrToInt(D7StringList.Strings[10]);
                  D7[11] := StrToInt(D7StringList.Strings[11]);
                  D7[12] := StrToInt(D7StringList.Strings[12]);

                  D8StringList := TStringList(ReturnList_n.Items[2]);
                  D8[0] := StrToInt(D8StringList.Strings[0]);
                  D8[1] := StrToInt(D8StringList.Strings[1]);
                  D8[2] := StrToInt(D8StringList.Strings[2]);
                  D8[3] := StrToInt(D8StringList.Strings[3]);
                  D8[4] := StrToInt(D8StringList.Strings[4]);
                  D8[5] := StrToInt(D8StringList.Strings[5]);
                  D8[6] := StrToInt(D8StringList.Strings[6]);
                  D8[7] := StrToInt(D8StringList.Strings[7]);
                  D8[8] := StrToInt(D8StringList.Strings[8]);
                  D8[9] := StrToInt(D8StringList.Strings[9]);
                  D8[10] := StrToInt(D8StringList.Strings[10]);
                  D8[11] := StrToInt(D8StringList.Strings[11]);
                  D8[12] := StrToInt(D8StringList.Strings[12]);
                end
                else
                begin
                  D6StringList := TStringList.Create;
                  D7StringList := TStringList.Create;
                  D8StringList := TStringList.Create;
                end;

                for i_n := 0 to ListAll_n.Count - 1 do
                begin
                  StringList_n := TStringList(ListAll_n.Items[i_n]);
                  StringList_n.Free;
                end;

                FreeAndNil(ListAll_n);
                FreeAndNil(List1_n);
                FreeAndNil(List2_n);
                FreeAndNil(List3_n);
                // FreeAndNil(List4_n);
                FreeAndNil(ReturnList_n);

                if D6StringList <> nil then
                begin
                  if D6StringList.Count <> 0 then
                  begin
                    FreeAndNil(D6StringList);
                  end;
                end;
                if D7StringList <> nil then
                begin
                  if D7StringList.Count <> 0 then
                  begin
                    FreeAndNil(D7StringList);
                  end;
                end;
                if D8StringList <> nil then
                begin
                  if D8StringList.Count <> 0 then
                  begin
                    FreeAndNil(D8StringList);
                  end;
                end;

              end;
            end
            else
            begin
              // 数量上判断，剩余20%都算合格
              if ((S6[0] + S6[1] + S6[2] + S6[3] + S6[4] + S6[5] + S6[6] + S6[7]
                + S6[8] + S6[9] + S6[10] + S6[11] + S6[12]) > 0) and
                ((S6[0] + S6[1] + S6[2] + S6[3] + S6[4] + S6[5] + S6[6] + S6[7]
                + S6[8] + S6[9] + S6[10] + S6[11] + S6[12]) <=
                (S[0] + S[1] + S[2] + S[3] + S[4] + S[5] + S[6] + S[7] + S[8] +
                S[9] + S[10] + S[11] + S[12]) * 0.10) then
              begin

                ThreeFlag := false;

                // WriteImportLog1('C:\1.txt', IntToStr(i) + '<<<begin2<<<' +
                // format('memory use: %d KB',
                // [GetProcessMemUse(GetCurrentProcessId)]));

                ListAll_n := TList.Create;
                List1_n := CalOnceSingle(1, S6, 2);
                List2_n := CalOnceSingle(2, S6, 2);
                List3_n := CalOnceSingle(3, S6, 2);
                // List4_n := CalOnceSingle(4, S6, 2);

                // WriteImportLog1('C:\1.txt', IntToStr(i) + '<<<end2<<<' +
                // format('memory use: %d KB',
                // [GetProcessMemUse(GetCurrentProcessId)]));

                for i_n := 0 to List1_n.Count - 1 do
                begin
                  StringList_n := TStringList(List1_n.Items[i_n]);
                  ListAll_n.Add(StringList_n);
                end;

                for i_n := 0 to List2_n.Count - 1 do
                begin
                  StringList_n := TStringList(List2_n.Items[i_n]);
                  ListAll_n.Add(StringList_n);
                end;

                for i_n := 0 to List3_n.Count - 1 do
                begin
                  StringList_n := TStringList(List3_n.Items[i_n]);
                  ListAll_n.Add(StringList_n);
                end;

                // for i_n := 0 to List4_n.Count - 1 do
                // begin
                // StringList_n := TStringList(List4_n.Items[i_n]);
                // ListAll_n.Add(StringList_n);
                // end;

                ReturnList_n := FillOnceReturn(ListAll_n, 1);
                if ReturnList_n.Count > 0 then
                begin
                  ThreeFlag := true;
                  D6StringList := TStringList(ReturnList_n.Items[0]);
                  D6[0] := StrToInt(D6StringList.Strings[0]);
                  D6[1] := StrToInt(D6StringList.Strings[1]);
                  D6[2] := StrToInt(D6StringList.Strings[2]);
                  D6[3] := StrToInt(D6StringList.Strings[3]);
                  D6[4] := StrToInt(D6StringList.Strings[4]);
                  D6[5] := StrToInt(D6StringList.Strings[5]);
                  D6[6] := StrToInt(D6StringList.Strings[6]);
                  D6[7] := StrToInt(D6StringList.Strings[7]);
                  D6[8] := StrToInt(D6StringList.Strings[8]);
                  D6[9] := StrToInt(D6StringList.Strings[9]);
                  D6[10] := StrToInt(D6StringList.Strings[10]);
                  D6[11] := StrToInt(D6StringList.Strings[11]);
                  D6[12] := StrToInt(D6StringList.Strings[12]);

                  D7StringList := TStringList(ReturnList_n.Items[1]);
                  D7[0] := StrToInt(D7StringList.Strings[0]);
                  D7[1] := StrToInt(D7StringList.Strings[1]);
                  D7[2] := StrToInt(D7StringList.Strings[2]);
                  D7[3] := StrToInt(D7StringList.Strings[3]);
                  D7[4] := StrToInt(D7StringList.Strings[4]);
                  D7[5] := StrToInt(D7StringList.Strings[5]);
                  D7[6] := StrToInt(D7StringList.Strings[6]);
                  D7[7] := StrToInt(D7StringList.Strings[7]);
                  D7[8] := StrToInt(D7StringList.Strings[8]);
                  D7[9] := StrToInt(D7StringList.Strings[9]);
                  D7[10] := StrToInt(D7StringList.Strings[10]);
                  D7[11] := StrToInt(D7StringList.Strings[11]);
                  D7[12] := StrToInt(D7StringList.Strings[12]);

                  D8StringList := TStringList(ReturnList_n.Items[2]);
                  D8[0] := StrToInt(D8StringList.Strings[0]);
                  D8[1] := StrToInt(D8StringList.Strings[1]);
                  D8[2] := StrToInt(D8StringList.Strings[2]);
                  D8[3] := StrToInt(D8StringList.Strings[3]);
                  D8[4] := StrToInt(D8StringList.Strings[4]);
                  D8[5] := StrToInt(D8StringList.Strings[5]);
                  D8[6] := StrToInt(D8StringList.Strings[6]);
                  D8[7] := StrToInt(D8StringList.Strings[7]);
                  D8[8] := StrToInt(D8StringList.Strings[8]);
                  D8[9] := StrToInt(D8StringList.Strings[9]);
                  D8[10] := StrToInt(D8StringList.Strings[10]);
                  D8[11] := StrToInt(D8StringList.Strings[11]);
                  D8[12] := StrToInt(D8StringList.Strings[12]);
                end
                else
                begin
                  D6StringList := TStringList.Create;
                  D7StringList := TStringList.Create;
                  D8StringList := TStringList.Create;
                end;

                for i_n := 0 to ListAll_n.Count - 1 do
                begin
                  StringList_n := TStringList(ListAll_n.Items[i_n]);
                  StringList_n.Free;
                end;

                FreeAndNil(ListAll_n);
                FreeAndNil(List1_n);
                FreeAndNil(List2_n);
                FreeAndNil(List3_n);
                // FreeAndNil(List4_n);
                FreeAndNil(ReturnList_n);

                if D6StringList <> nil then
                begin
                  if D6StringList.Count <> 0 then
                  begin
                    FreeAndNil(D6StringList);
                  end;
                end;
                if D7StringList <> nil then
                begin
                  if D7StringList.Count <> 0 then
                  begin
                    FreeAndNil(D7StringList);
                  end;
                end;
                if D8StringList <> nil then
                begin
                  if D8StringList.Count <> 0 then
                  begin
                    FreeAndNil(D8StringList);
                  end;
                end;

              end;
            end;

          end;

          if S[0] + S[1] + S[2] + S[3] + S[4] + S[5] + S[6] + S[7] + S[8] + S[9]
            + S[10] + S[11] + S[12] > 7500 then
          begin
            if ThreeFlag = false then
            begin
              if ((S6[0] + S6[1] + S6[2] + S6[3] + S6[4] + S6[5] + S6[6] + S6[7]
                + S6[8] + S6[9] + S6[10] + S6[11] + S6[12]) > 0) and
                ((S6[0] + S6[1] + S6[2] + S6[3] + S6[4] + S6[5] + S6[6] + S6[7]
                + S6[8] + S6[9] + S6[10] + S6[11] + S6[12]) <=
                (S[0] + S[1] + S[2] + S[3] + S[4] + S[5] + S[6] + S[7] + S[8] +
                S[9] + S[10] + S[11] + S[12] - 6600)) then
              begin
                ListAll_n := TList.Create;

                // WriteImportLog1('C:\1.txt',
                // inttostr(i) + '<<<CalOnceFiveSingle<<<' +
                // format('memory use: %d KB',
                // [GetProcessMemUse(GetCurrentProcessId)]));

                List1_n := CalOnceFiveSingle(1, S6, 2);
                List2_n := CalOnceFiveSingle(2, S6, 2);
                List3_n := CalOnceFiveSingle(3, S6, 2);
                // List4_n := CalOnceFiveSingle(4, S6, 2);

                for i_n := 0 to List1_n.Count - 1 do
                begin
                  StringList_n := TStringList(List1_n.Items[i_n]);
                  ListAll_n.Add(StringList_n);
                end;

                for i_n := 0 to List2_n.Count - 1 do
                begin
                  StringList_n := TStringList(List2_n.Items[i_n]);
                  ListAll_n.Add(StringList_n);
                end;

                for i_n := 0 to List3_n.Count - 1 do
                begin
                  StringList_n := TStringList(List3_n.Items[i_n]);
                  ListAll_n.Add(StringList_n);
                end;

                // for i_n := 0 to List4_n.Count - 1 do
                // begin
                // StringList_n := TStringList(List4_n.Items[i_n]);
                // ListAll_n.Add(StringList_n);
                // end;

                ReturnList_n := FillOnceFiveReturn(ListAll_n, 1);
                if ReturnList_n.Count > 0 then
                begin
                  D6StringList := TStringList(ReturnList_n.Items[0]);
                  D6[0] := StrToInt(D6StringList.Strings[0]);
                  D6[1] := StrToInt(D6StringList.Strings[1]);
                  D6[2] := StrToInt(D6StringList.Strings[2]);
                  D6[3] := StrToInt(D6StringList.Strings[3]);
                  D6[4] := StrToInt(D6StringList.Strings[4]);
                  D6[5] := StrToInt(D6StringList.Strings[5]);
                  D6[6] := StrToInt(D6StringList.Strings[6]);
                  D6[7] := StrToInt(D6StringList.Strings[7]);
                  D6[8] := StrToInt(D6StringList.Strings[8]);
                  D6[9] := StrToInt(D6StringList.Strings[9]);
                  D6[10] := StrToInt(D6StringList.Strings[10]);
                  D6[11] := StrToInt(D6StringList.Strings[11]);
                  D6[12] := StrToInt(D6StringList.Strings[12]);

                  D7StringList := TStringList(ReturnList_n.Items[1]);
                  D7[0] := StrToInt(D7StringList.Strings[0]);
                  D7[1] := StrToInt(D7StringList.Strings[1]);
                  D7[2] := StrToInt(D7StringList.Strings[2]);
                  D7[3] := StrToInt(D7StringList.Strings[3]);
                  D7[4] := StrToInt(D7StringList.Strings[4]);
                  D7[5] := StrToInt(D7StringList.Strings[5]);
                  D7[6] := StrToInt(D7StringList.Strings[6]);
                  D7[7] := StrToInt(D7StringList.Strings[7]);
                  D7[8] := StrToInt(D7StringList.Strings[8]);
                  D7[9] := StrToInt(D7StringList.Strings[9]);
                  D7[10] := StrToInt(D7StringList.Strings[10]);
                  D7[11] := StrToInt(D7StringList.Strings[11]);
                  D7[12] := StrToInt(D7StringList.Strings[12]);

                  D8StringList := TStringList(ReturnList_n.Items[2]);
                  D8[0] := StrToInt(D8StringList.Strings[0]);
                  D8[1] := StrToInt(D8StringList.Strings[1]);
                  D8[2] := StrToInt(D8StringList.Strings[2]);
                  D8[3] := StrToInt(D8StringList.Strings[3]);
                  D8[4] := StrToInt(D8StringList.Strings[4]);
                  D8[5] := StrToInt(D8StringList.Strings[5]);
                  D8[6] := StrToInt(D8StringList.Strings[6]);
                  D8[7] := StrToInt(D8StringList.Strings[7]);
                  D8[8] := StrToInt(D8StringList.Strings[8]);
                  D8[9] := StrToInt(D8StringList.Strings[9]);
                  D8[10] := StrToInt(D8StringList.Strings[10]);
                  D8[11] := StrToInt(D8StringList.Strings[11]);
                  D8[12] := StrToInt(D8StringList.Strings[12]);

                  D9StringList := TStringList(ReturnList_n.Items[3]);
                  D9[0] := StrToInt(D9StringList.Strings[0]);
                  D9[1] := StrToInt(D9StringList.Strings[1]);
                  D9[2] := StrToInt(D9StringList.Strings[2]);
                  D9[3] := StrToInt(D9StringList.Strings[3]);
                  D9[4] := StrToInt(D9StringList.Strings[4]);
                  D9[5] := StrToInt(D9StringList.Strings[5]);
                  D9[6] := StrToInt(D9StringList.Strings[6]);
                  D9[7] := StrToInt(D9StringList.Strings[7]);
                  D9[8] := StrToInt(D9StringList.Strings[8]);
                  D9[9] := StrToInt(D9StringList.Strings[9]);
                  D9[10] := StrToInt(D9StringList.Strings[10]);
                  D9[11] := StrToInt(D9StringList.Strings[11]);
                  D9[12] := StrToInt(D9StringList.Strings[12]);

                  D10StringList := TStringList(ReturnList_n.Items[4]);
                  D10[0] := StrToInt(D10StringList.Strings[0]);
                  D10[1] := StrToInt(D10StringList.Strings[1]);
                  D10[2] := StrToInt(D10StringList.Strings[2]);
                  D10[3] := StrToInt(D10StringList.Strings[3]);
                  D10[4] := StrToInt(D10StringList.Strings[4]);
                  D10[5] := StrToInt(D10StringList.Strings[5]);
                  D10[6] := StrToInt(D10StringList.Strings[6]);
                  D10[7] := StrToInt(D10StringList.Strings[7]);
                  D10[8] := StrToInt(D10StringList.Strings[8]);
                  D10[9] := StrToInt(D10StringList.Strings[9]);
                  D10[10] := StrToInt(D10StringList.Strings[10]);
                  D10[11] := StrToInt(D10StringList.Strings[11]);
                  D10[12] := StrToInt(D10StringList.Strings[12]);
                end
                else
                begin
                  D6StringList := TStringList.Create;
                  D7StringList := TStringList.Create;
                  D8StringList := TStringList.Create;
                  D9StringList := TStringList.Create;
                  D10StringList := TStringList.Create;
                end;

                for i_n := 0 to ListAll_n.Count - 1 do
                begin
                  StringList_n := TStringList(ListAll_n.Items[i_n]);
                  StringList_n.Free;
                end;

                FreeAndNil(ListAll_n);
                FreeAndNil(List1_n);
                FreeAndNil(List2_n);
                FreeAndNil(List3_n);
                // FreeAndNil(List4_n);
                FreeAndNil(ReturnList_n);

                // WriteImportLog1('C:\1.txt', 'free end' + '<<<qqqqqq<<<' +
                // format('memory use: %d KB',
                // [GetProcessMemUse(GetCurrentProcessId)]));

                if D6StringList <> nil then
                begin
                  if D6StringList.Count <> 0 then
                  begin
                    FreeAndNil(D6StringList);
                  end;
                end;
                if D7StringList <> nil then
                begin
                  if D7StringList.Count <> 0 then
                  begin
                    FreeAndNil(D7StringList);
                  end;
                end;
                if D8StringList <> nil then
                begin
                  if D8StringList.Count <> 0 then
                  begin
                    FreeAndNil(D8StringList);
                  end;
                end;
                if D9StringList <> nil then
                begin
                  if D9StringList.Count <> 0 then
                  begin
                    FreeAndNil(D9StringList);
                  end;
                end;
                if D10StringList <> nil then
                begin
                  if D10StringList.Count <> 0 then
                  begin
                    FreeAndNil(D10StringList);
                  end;
                end;

              end;
            end;
          end;

        end;

        if flag = 0 then
        begin

          // 最后在校验下是否全部分配掉了
          Sum1 := 0;
          Sum2 := 0;
          SumD1 := 0;
          SumD2 := 0;
          SumD3 := 0;
          SumD4 := 0;
          SumD5 := 0;
          SumD6 := 0;
          SumD7 := 0;
          SumD8 := 0;
          SumD9 := 0;
          SumD10 := 0;

          StringList_S := TStringList.Create;
          StringList_D1 := TStringList.Create;
          StringList_D2 := TStringList.Create;
          StringList_D3 := TStringList.Create;
          StringList_D4 := TStringList.Create;
          StringList_D5 := TStringList.Create;
          StringList_D6 := TStringList.Create;
          StringList_D7 := TStringList.Create;
          StringList_D8 := TStringList.Create;
          StringList_D9 := TStringList.Create;
          StringList_D10 := TStringList.Create;

          for o := 0 to CMPZSUM - 1 do
          begin
            StringList_S.Add(IntToStr(S[o]));
            StringList_D1.Add(IntToStr(D1[o]));
            StringList_D2.Add(IntToStr(D2[o]));
            StringList_D3.Add(IntToStr(D3[o]));
            StringList_D4.Add(IntToStr(D4[o]));
            StringList_D5.Add(IntToStr(D5[o]));
            StringList_D6.Add(IntToStr(D6[o]));
            StringList_D7.Add(IntToStr(D7[o]));
            StringList_D8.Add(IntToStr(D8[o]));
            StringList_D9.Add(IntToStr(D9[o]));
            StringList_D10.Add(IntToStr(D10[o]));
            Sum1 := Sum1 + S[o];
            Sum2 := Sum2 + D1[o] + D2[o] + D3[o] + D4[o] + D5[o] + D6[o] + D7[o]
              + D8[o] + D9[o] + D10[o];
            SumD1 := SumD1 + D1[o];
            SumD2 := SumD2 + D2[o];
            SumD3 := SumD3 + D3[o];
            SumD4 := SumD4 + D4[o];
            SumD5 := SumD5 + D5[o];
            SumD6 := SumD6 + D6[o];
            SumD7 := SumD7 + D7[o];
            SumD8 := SumD8 + D8[o];
            SumD9 := SumD9 + D9[o];
            SumD10 := SumD10 + D10[o];
          end;

          if Sum1 = Sum2 then
          begin
            // 重新计算每个Dcount

            CalDcount1 := CalDcount(StringList_D1);
            CalDCount2 := CalDcount(StringList_D2);
            CalDCount3 := CalDcount(StringList_D3);
            CalDCount4 := CalDcount(StringList_D4);
            CalDCount5 := CalDcount(StringList_D5);
            CalDCount6 := CalDcount(StringList_D6);
            CalDCount7 := CalDcount(StringList_D7);
            CalDCount8 := CalDcount(StringList_D8);
            CalDCount9 := CalDcount(StringList_D9);
            CalDCount10 := CalDcount(StringList_D10);
            CalDCountSum := CalDcount1 + CalDCount2 + CalDCount3 + CalDCount4 +
              CalDCount5 + CalDCount6 + CalDCount7 + CalDCount8 + CalDCount9 +
              CalDCount10;

            StringList_S.Add(IntToStr(CalDCountSum));
            StringList_D1.Add(IntToStr(CalDcount1));
            StringList_D2.Add(IntToStr(CalDCount2));
            StringList_D3.Add(IntToStr(CalDCount3));
            StringList_D4.Add(IntToStr(CalDCount4));
            StringList_D5.Add(IntToStr(CalDCount5));
            StringList_D6.Add(IntToStr(CalDCount6));
            StringList_D7.Add(IntToStr(CalDCount7));
            StringList_D8.Add(IntToStr(CalDCount8));
            StringList_D9.Add(IntToStr(CalDCount9));
            StringList_D10.Add(IntToStr(CalDCount10));

            StringList_D1.Add(IntToStr(SumD1));
            StringList_D2.Add(IntToStr(SumD2));
            StringList_D3.Add(IntToStr(SumD3));
            StringList_D4.Add(IntToStr(SumD4));
            StringList_D5.Add(IntToStr(SumD5));
            StringList_D6.Add(IntToStr(SumD6));
            StringList_D7.Add(IntToStr(SumD7));
            StringList_D8.Add(IntToStr(SumD8));
            StringList_D9.Add(IntToStr(SumD9));
            StringList_D10.Add(IntToStr(SumD10));
            math75Flag := true;

            if CalDcount1 > 1 then
            begin
              if (SumD1 / CalDcount1 > 75) then
              begin

                if (CalDcount1 >= 3) then
                begin
                  math75Flag := true;
                end
                else
                begin
                  math75Flag := false;
                end;

              end;
            end;

            if math75Flag then
            begin
              if CalDCount2 > 1 then
              begin
                if (SumD2 / CalDCount2 > 75) then
                begin
                  if (CalDCount2 >= 3) then
                  begin
                    math75Flag := true;
                  end
                  else
                  begin
                    math75Flag := false;
                  end;
                end;
              end;
            end;

            if math75Flag then
            begin
              if CalDCount3 > 1 then
              begin
                if (SumD3 / CalDCount3 > 75) then
                begin
                  if (CalDCount3 >= 3) then
                  begin
                    math75Flag := true;
                  end
                  else
                  begin
                    math75Flag := false;
                  end;
                end;
              end;
            end;

            if math75Flag then
            begin
              if CalDCount4 > 1 then
              begin
                if (SumD4 / CalDCount4 > 75) then
                begin
                  if (CalDCount4 >= 3) then
                  begin
                    math75Flag := true;
                  end
                  else
                  begin
                    math75Flag := false;
                  end;
                end;
              end;
            end;

            if math75Flag then
            begin
              if CalDCount5 > 1 then
              begin
                if (SumD5 / CalDCount5 > 75) then
                begin
                  if (CalDCount5 >= 3) then
                  begin
                    math75Flag := true;
                  end
                  else
                  begin
                    math75Flag := false;
                  end;
                end;
              end;
            end;

            if math75Flag then
            begin
              if CalDCount6 > 1 then
              begin
                if (SumD6 / CalDCount6 > 75) then
                begin
                  if (CalDCount6 >= 3) then
                  begin
                    math75Flag := true;
                  end
                  else
                  begin
                    math75Flag := false;
                  end;
                end;
              end;
            end;

            if math75Flag then
            begin
              if CalDCount7 > 1 then
              begin
                if (SumD7 / CalDCount7 > 75) then
                begin
                  if (CalDCount7 >= 3) then
                  begin
                    math75Flag := true;
                  end
                  else
                  begin
                    math75Flag := false;
                  end;
                end;
              end;
            end;

            if math75Flag then
            begin
              if CalDCount8 > 1 then
              begin
                if (SumD8 / CalDCount8 > 75) then
                begin
                  if (CalDCount8 >= 3) then
                  begin
                    math75Flag := true;
                  end
                  else
                  begin
                    math75Flag := false;
                  end;
                end;
              end;
            end;

            if math75Flag then
            begin
              if CalDCount9 > 1 then
              begin
                if (SumD9 / CalDCount9 > 75) then
                begin
                  if (CalDCount9 >= 3) then
                  begin
                    math75Flag := true;
                  end
                  else
                  begin
                    math75Flag := false;
                  end;
                end;
              end;
            end;

            if math75Flag then
            begin
              if CalDCount10 > 1 then
              begin
                if (SumD10 / CalDCount10 > 75) then
                begin
                  if (CalDCount10 >= 3) then
                  begin
                    math75Flag := true;
                  end
                  else
                  begin
                    math75Flag := false;
                  end;
                end;
              end;
            end;

            WriteImportLog1('C:\1.txt', '----------------');

            WriteImportLog1('C:\1.txt', IntToStr(D1[0]) + ',' + IntToStr(D1[1])
              + ',' + IntToStr(D1[2]) + ',' + IntToStr(D1[3]) + ',' +
              IntToStr(D1[4]) + ',' + IntToStr(D1[5]) + ',' + IntToStr(D1[6]) +
              ',' + IntToStr(D1[7]) + ',' + IntToStr(D1[8]) + ',' +
              IntToStr(D1[9]) + ',');

            WriteImportLog1('C:\1.txt', IntToStr(D2[0]) + ',' + IntToStr(D2[1])
              + ',' + IntToStr(D2[2]) + ',' + IntToStr(D2[3]) + ',' +
              IntToStr(D2[4]) + ',' + IntToStr(D2[5]) + ',' + IntToStr(D2[6]) +
              ',' + IntToStr(D2[7]) + ',' + IntToStr(D2[8]) + ',' +
              IntToStr(D2[9]) + ',');

            WriteImportLog1('C:\1.txt', IntToStr(D3[0]) + ',' + IntToStr(D3[1])
              + ',' + IntToStr(D3[2]) + ',' + IntToStr(D3[3]) + ',' +
              IntToStr(D3[4]) + ',' + IntToStr(D3[5]) + ',' + IntToStr(D3[6]) +
              ',' + IntToStr(D3[7]) + ',' + IntToStr(D3[8]) + ',' +
              IntToStr(D3[9]) + ',');

            WriteImportLog1('C:\1.txt', IntToStr(D4[0]) + ',' + IntToStr(D4[1])
              + ',' + IntToStr(D4[2]) + ',' + IntToStr(D4[3]) + ',' +
              IntToStr(D4[4]) + ',' + IntToStr(D4[5]) + ',' + IntToStr(D4[6]) +
              ',' + IntToStr(D4[7]) + ',' + IntToStr(D4[8]) + ',' +
              IntToStr(D4[9]) + ',');

            WriteImportLog1('C:\1.txt', IntToStr(D5[0]) + ',' + IntToStr(D5[1])
              + ',' + IntToStr(D5[2]) + ',' + IntToStr(D5[3]) + ',' +
              IntToStr(D5[4]) + ',' + IntToStr(D5[5]) + ',' + IntToStr(D5[6]) +
              ',' + IntToStr(D5[7]) + ',' + IntToStr(D5[8]) + ',' +
              IntToStr(D5[9]) + ',');

            WriteImportLog1('C:\1.txt', IntToStr(D6[0]) + ',' + IntToStr(D6[1])
              + ',' + IntToStr(D6[2]) + ',' + IntToStr(D6[3]) + ',' +
              IntToStr(D6[4]) + ',' + IntToStr(D6[5]) + ',' + IntToStr(D6[6]) +
              ',' + IntToStr(D6[7]) + ',' + IntToStr(D6[8]) + ',' +
              IntToStr(D6[9]) + ',');

            WriteImportLog1('C:\1.txt', IntToStr(D7[0]) + ',' + IntToStr(D7[1])
              + ',' + IntToStr(D7[2]) + ',' + IntToStr(D7[3]) + ',' +
              IntToStr(D7[4]) + ',' + IntToStr(D7[5]) + ',' + IntToStr(D7[6]) +
              ',' + IntToStr(D7[7]) + ',' + IntToStr(D7[8]) + ',' +
              IntToStr(D7[9]) + ',');

            WriteImportLog1('C:\1.txt', IntToStr(D8[0]) + ',' + IntToStr(D8[1])
              + ',' + IntToStr(D8[2]) + ',' + IntToStr(D8[3]) + ',' +
              IntToStr(D8[4]) + ',' + IntToStr(D8[5]) + ',' + IntToStr(D8[6]) +
              ',' + IntToStr(D8[7]) + ',' + IntToStr(D8[8]) + ',' +
              IntToStr(D8[9]) + ',');

            WriteImportLog1('C:\1.txt', '----------------');

            // 这里对于重复数据的判断是否多余，还需要做个验证
            if math75Flag = true then
            begin

              AddRoleFlag1 := false;
              AddRoleFlag2 := false;
              // 加入判断1 是否满足最大投缸量
              // 加入判断2 如果有规则1369，则需要计算
              if (SumD1 * G_YJL < G_MaxTg) and (SumD2 * G_YJL < G_MaxTg) and
                (SumD3 * G_YJL < G_MaxTg) and (SumD4 * G_YJL < G_MaxTg) and
                (SumD5 * G_YJL < G_MaxTg) and (SumD6 * G_YJL < G_MaxTg) and
                (SumD7 * G_YJL < G_MaxTg) and (SumD8 * G_YJL < G_MaxTg) and
                (SumD9 * G_YJL < G_MaxTg) and (SumD10 * G_YJL < G_MaxTg) then
              begin
                AddRoleFlag1 := true;
              end;

              if G_Gz1 = '369排' then
              begin
                if ((CalDcount1 = 0) or (CalDcount1 = 1) or
                  ((CalDcount1 mod 3) = 0)) and
                  ((CalDCount2 = 0) or (CalDCount2 = 1) or
                  ((CalDCount2 mod 3) = 0)) and
                  ((CalDCount3 = 0) or (CalDCount3 = 1) or
                  ((CalDCount3 mod 3) = 0)) and
                  ((CalDCount4 = 0) or (CalDCount4 = 1) or
                  ((CalDCount4 mod 3) = 0)) and
                  ((CalDCount5 = 0) or (CalDCount5 = 1) or
                  ((CalDCount5 mod 3) = 0)) and
                  ((CalDCount6 = 0) or (CalDCount6 = 1) or
                  ((CalDCount6 mod 3) = 0)) and
                  ((CalDCount7 = 0) or (CalDCount7 = 1) or
                  ((CalDCount7 mod 3) = 0)) and
                  ((CalDCount8 = 0) or (CalDCount8 = 1) or
                  ((CalDCount8 mod 3) = 0)) and
                  ((CalDCount9 = 0) or (CalDCount9 = 1) or
                  ((CalDCount9 mod 3) = 0)) and
                  ((CalDCount10 = 0) or (CalDCount10 = 1) or
                  ((CalDCount10 mod 3) = 0)) then
                begin
                  AddRoleFlag2 := true;
                end;
              end
              else
              begin
                if ((CalDcount1 = 0) or (CalDcount1 > 1)) and
                  ((CalDCount2 = 0) or (CalDCount2 > 1)) and
                  ((CalDCount3 = 0) or (CalDCount3 > 1)) and
                  ((CalDCount4 = 0) or (CalDCount4 > 1)) and
                  ((CalDCount5 = 0) or (CalDCount5 > 1)) and
                  ((CalDCount6 = 0) or (CalDCount6 > 1)) and
                  ((CalDCount7 = 0) or (CalDCount7 > 1)) and
                  ((CalDCount8 = 0) or (CalDCount8 > 1)) and
                  ((CalDCount9 = 0) or (CalDCount9 > 1)) and
                  ((CalDCount10 = 0) or (CalDCount10 > 1)) then
                begin
                  AddRoleFlag2 := true;
                end
                else
                begin
                  AddRoleFlag2 := false;
                end;
              end;

              if AddRoleFlag1 and AddRoleFlag2 then
              begin

                // WriteImportLog1('C:\1.txt', '----------------');
                //
                // WriteImportLog1('C:\1.txt', IntToStr(D1[0]) + ',' +
                // IntToStr(D1[1]) + ',' + IntToStr(D1[2]) + ',' + IntToStr(D1[3]
                // ) + ',' + IntToStr(D1[4]) + ',' + IntToStr(D1[5]) + ',' +
                // IntToStr(D1[6]) + ',' + IntToStr(D1[7]) + ',' + IntToStr(D1[8]
                // ) + ',' + IntToStr(D1[9]) + ',');
                //
                // WriteImportLog1('C:\1.txt', '----------------');

                CStringList_S := TStringList.Create;
                for o := 0 to StringList_S.Count - 1 do
                begin
                  CStringList_S.Add(StringList_S.Strings[o]);
                end;

                CStringList_D1 := TStringList.Create;
                for o := 0 to StringList_D1.Count - 1 do
                begin
                  CStringList_D1.Add(StringList_D1.Strings[o]);
                end;

                CStringList_D2 := TStringList.Create;
                for o := 0 to StringList_D2.Count - 1 do
                begin
                  CStringList_D2.Add(StringList_D2.Strings[o]);
                end;

                CStringList_D3 := TStringList.Create;
                for o := 0 to StringList_D3.Count - 1 do
                begin
                  CStringList_D3.Add(StringList_D3.Strings[o]);
                end;

                CStringList_D4 := TStringList.Create;
                for o := 0 to StringList_D4.Count - 1 do
                begin
                  CStringList_D4.Add(StringList_D4.Strings[o]);
                end;

                CStringList_D5 := TStringList.Create;
                for o := 0 to StringList_D5.Count - 1 do
                begin
                  CStringList_D5.Add(StringList_D5.Strings[o]);
                end;

                CStringList_D6 := TStringList.Create;
                for o := 0 to StringList_D6.Count - 1 do
                begin
                  CStringList_D6.Add(StringList_D6.Strings[o]);
                end;

                CStringList_D7 := TStringList.Create;
                for o := 0 to StringList_D7.Count - 1 do
                begin
                  CStringList_D7.Add(StringList_D7.Strings[o]);
                end;

                CStringList_D8 := TStringList.Create;
                for o := 0 to StringList_D8.Count - 1 do
                begin
                  CStringList_D8.Add(StringList_D8.Strings[o]);
                end;

                CStringList_D9 := TStringList.Create;
                for o := 0 to StringList_D9.Count - 1 do
                begin
                  CStringList_D9.Add(StringList_D9.Strings[o]);
                end;

                CStringList_D10 := TStringList.Create;
                for o := 0 to StringList_D10.Count - 1 do
                begin
                  CStringList_D10.Add(StringList_D10.Strings[o]);
                end;

                // CStringList_S := StringList_S;
                // CStringList_D1 := StringList_D1;
                // CStringList_D2 := StringList_D2;
                // CStringList_D3 := StringList_D3;
                // CStringList_D4 := StringList_D4;
                // CStringList_D5 := StringList_D5;
                // CStringList_D6 := StringList_D6;
                // CStringList_D7 := StringList_D7;
                // CStringList_D8 := StringList_D8;
                // CStringList_D9 := StringList_D9;
                // CStringList_D10 := StringList_D10;

                List.Add(CStringList_S);
                List.Add(CStringList_D1);
                List.Add(CStringList_D2);
                List.Add(CStringList_D3);
                List.Add(CStringList_D4);
                List.Add(CStringList_D5);
                List.Add(CStringList_D6);
                List.Add(CStringList_D7);
                List.Add(CStringList_D8);
                List.Add(CStringList_D9);
                List.Add(CStringList_D10);

              end;

            end;

          end;

          FreeAndNil(StringList_S);
          FreeAndNil(StringList_D1);
          FreeAndNil(StringList_D2);
          FreeAndNil(StringList_D3);
          FreeAndNil(StringList_D4);
          FreeAndNil(StringList_D5);
          FreeAndNil(StringList_D6);
          FreeAndNil(StringList_D7);
          FreeAndNil(StringList_D8);
          FreeAndNil(StringList_D9);
          FreeAndNil(StringList_D10);

        end;
      end;

    end;

  end;

  // 读取数据
  setlength(S, 0);
  setlength(S1, 0);
  setlength(S2, 0);
  setlength(S3, 0);
  setlength(S4, 0);
  setlength(S5, 0);
  setlength(S6, 0);
  setlength(S7, 0);
  setlength(S8, 0);
  setlength(S9, 0);
  setlength(S10, 0);
  setlength(D1, 0);
  setlength(D2, 0);
  setlength(D3, 0);
  setlength(D4, 0);
  setlength(D5, 0);
  setlength(D6, 0);
  setlength(D7, 0);
  setlength(D8, 0);
  setlength(D9, 0);
  setlength(D10, 0);
  setlength(S1Userd, 0);
  setlength(S2Userd, 0);
  setlength(S3Userd, 0);
  setlength(S4Userd, 0);
  setlength(S5Userd, 0);
  setlength(S6Userd, 0);
  setlength(S7Userd, 0);
  setlength(S8Userd, 0);
  setlength(S9Userd, 0);
  setlength(S10Userd, 0);

  setlength(C, 0);

  setlength(Role1, 0);
  setlength(Role2, 0);
  setlength(Role3, 0);
  setlength(Role4, 0);
  setlength(Role5, 0);
  setlength(Role6, 0);
  setlength(Role7, 0);
  setlength(Role8, 0);
  setlength(Role9, 0);
  setlength(Role10, 0);
  result := List;

end;

constructor TThreadPdCal.Create;
begin
  // 优化：不再需要创建缓存对象，使用静态缓存
end;

destructor TThreadPdCal.Destroy;
begin
  inherited;
end;

procedure TThreadPdCal.FillGrid2(S, D1StringList, D2StringList,
  D3StringList: TStringList);
var
  S1StringList_D1, S1StringList_D2, S1StringList_D3: TStringList;
  S2StringList_D1, S2StringList_D2, S2StringList_D3: TStringList;
  S3StringList_D1, S3StringList_D2, S3StringList_D3: TStringList;
  S4StringList_D1, S4StringList_D2, S4StringList_D3: TStringList;
  S5StringList_D1, S5StringList_D2, S5StringList_D3: TStringList;
  SSALL1StringList, SSALL2StringList, SSALL3StringList, SSALL4StringList,
    SSALL5StringList: TStringList;
  Kc1StringList, Kc2StringList, Kc3StringList, Kc4StringList,
    Kc5StringList: TStringList;
  k: Integer;
  SD1, SD2, SD3: Array of Integer;
  D1, D2, D3: Array of Integer;
  MATERIALFZID1, MATERIALFZID2, MATERIALFZID3, MATERIALFZID4,
    MATERIALFZID5: Integer;
  cmtypenum1, cmtypenum2, cmtypenum3, cmtypenum4, cmtypenum5: Integer;
begin
  S1StringList_D1 := TStringList.Create;
  S1StringList_D2 := TStringList.Create;
  S1StringList_D3 := TStringList.Create;

  S2StringList_D1 := TStringList.Create;
  S2StringList_D2 := TStringList.Create;
  S2StringList_D3 := TStringList.Create;

  S3StringList_D1 := TStringList.Create;
  S3StringList_D2 := TStringList.Create;
  S3StringList_D3 := TStringList.Create;

  S4StringList_D1 := TStringList.Create;
  S4StringList_D2 := TStringList.Create;
  S4StringList_D3 := TStringList.Create;

  S5StringList_D1 := TStringList.Create;
  S5StringList_D2 := TStringList.Create;
  S5StringList_D3 := TStringList.Create;

  setlength(SD1, CMPZSUM);
  setlength(SD2, CMPZSUM);
  setlength(SD3, CMPZSUM);

  // 优化：使用批量转换替代逐个StrToInt调用
  setlength(D1, CMPZSUM);
  D1 := TOptimizationHelper.StringListToIntArray(D1StringList, 0, CMPZSUM);

  setlength(D2, CMPZSUM);
  D2 := TOptimizationHelper.StringListToIntArray(D2StringList, 0, CMPZSUM);

  setlength(D3, CMPZSUM);
  D3 := TOptimizationHelper.StringListToIntArray(D3StringList, 0, CMPZSUM);

  // D1处理
  for k := 0 to CMPZSUM - 1 do
  begin
    if D1[k] > 0 then
    begin
      if SS1[k] - D1[k] >= 0 then
      begin
        SD1[k] := D1[k];
        D1[k] := 0;
        SS1[k] := SS1[k] - SD1[k];
        S1StringList_D1.Add(IntToStr(SD1[k]));
      end
      else
      begin
        SD1[k] := SS1[k];
        D1[k] := D1[k] - SD1[k];
        SS1[k] := SS1[k] - SD1[k];
        S1StringList_D1.Add(IntToStr(SD1[k]));
      end;
    end
    else
    begin
      S1StringList_D1.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D1[k] > 0 then
    begin
      if SS2[k] - D1[k] >= 0 then
      begin
        SD1[k] := D1[k];
        D1[k] := 0;
        SS2[k] := SS2[k] - SD1[k];
        S2StringList_D1.Add(IntToStr(SD1[k]));
      end
      else
      begin
        SD1[k] := SS2[k];
        D1[k] := D1[k] - SD1[k];
        SS2[k] := SS2[k] - SD1[k];
        S2StringList_D1.Add(IntToStr(SD1[k]));
      end;
    end
    else
    begin
      S2StringList_D1.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D1[k] > 0 then
    begin
      if SS3[k] - D1[k] >= 0 then
      begin
        SD1[k] := D1[k];
        D1[k] := 0;
        SS3[k] := SS3[k] - SD1[k];
        S3StringList_D1.Add(IntToStr(SD1[k]));
      end
      else
      begin
        SD1[k] := SS3[k];
        D1[k] := D1[k] - SD1[k];
        SS3[k] := SS3[k] - SD1[k];
        S3StringList_D1.Add(IntToStr(SD1[k]));
      end;
    end
    else
    begin
      S3StringList_D1.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D1[k] > 0 then
    begin
      if SS4[k] - D1[k] >= 0 then
      begin
        SD1[k] := D1[k];
        D1[k] := 0;
        SS4[k] := SS4[k] - SD1[k];
        S4StringList_D1.Add(IntToStr(SD1[k]));
      end
      else
      begin
        SD1[k] := SS4[k];
        D1[k] := D1[k] - SD1[k];
        SS4[k] := SS4[k] - SD1[k];
        S4StringList_D1.Add(IntToStr(SD1[k]));
      end;
    end
    else
    begin
      S4StringList_D1.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D1[k] > 0 then
    begin
      if SS5[k] - D1[k] >= 0 then
      begin
        SD1[k] := D1[k];
        D1[k] := 0;
        SS5[k] := SS5[k] - SD1[k];
        S5StringList_D1.Add(IntToStr(SD1[k]));
      end
      else
      begin
        SD1[k] := SS5[k];
        D1[k] := D1[k] - SD1[k];
        SS5[k] := SS5[k] - SD1[k];
        S5StringList_D1.Add(IntToStr(SD1[k]));
      end;
    end
    else
    begin
      S5StringList_D1.Add('0');
    end;
  end;

  // D2处理
  for k := 0 to CMPZSUM - 1 do
  begin
    if D2[k] > 0 then
    begin
      if SS1[k] - D2[k] >= 0 then
      begin
        SD2[k] := D2[k];
        D2[k] := 0;
        SS1[k] := SS1[k] - SD2[k];
        S1StringList_D2.Add(IntToStr(SD2[k]));
      end
      else
      begin
        SD2[k] := SS1[k];
        D2[k] := D2[k] - SD2[k];
        SS1[k] := SS1[k] - SD2[k];
        S1StringList_D2.Add(IntToStr(SD2[k]));
      end;
    end
    else
    begin
      S1StringList_D2.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D2[k] > 0 then
    begin
      if SS2[k] - D2[k] >= 0 then
      begin
        SD2[k] := D2[k];
        D2[k] := 0;
        SS2[k] := SS2[k] - SD2[k];
        S2StringList_D2.Add(IntToStr(SD2[k]));
      end
      else
      begin
        SD2[k] := SS2[k];
        D2[k] := D2[k] - SD2[k];
        SS2[k] := SS2[k] - SD2[k];
        S2StringList_D2.Add(IntToStr(SD2[k]));
      end;
    end
    else
    begin
      S2StringList_D2.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D2[k] > 0 then
    begin
      if SS3[k] - D2[k] >= 0 then
      begin
        SD2[k] := D2[k];
        D2[k] := 0;
        SS3[k] := SS3[k] - SD2[k];
        S3StringList_D2.Add(IntToStr(SD2[k]));
      end
      else
      begin
        SD2[k] := SS3[k];
        D2[k] := D2[k] - SD2[k];
        SS3[k] := SS3[k] - SD2[k];
        S3StringList_D2.Add(IntToStr(SD2[k]));
      end;
    end
    else
    begin
      S3StringList_D2.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D2[k] > 0 then
    begin
      if SS4[k] - D2[k] >= 0 then
      begin
        SD2[k] := D2[k];
        D2[k] := 0;
        SS4[k] := SS4[k] - SD2[k];
        S4StringList_D2.Add(IntToStr(SD2[k]));
      end
      else
      begin
        SD2[k] := SS4[k];
        D2[k] := D2[k] - SD2[k];
        SS4[k] := SS4[k] - SD2[k];
        S4StringList_D2.Add(IntToStr(SD2[k]));
      end;
    end
    else
    begin
      S4StringList_D2.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D2[k] > 0 then
    begin
      if SS5[k] - D2[k] >= 0 then
      begin
        SD2[k] := D2[k];
        D2[k] := 0;
        SS5[k] := SS5[k] - SD2[k];
        S5StringList_D2.Add(IntToStr(SD2[k]));
      end
      else
      begin
        SD2[k] := SS5[k];
        D2[k] := D2[k] - SD2[k];
        SS5[k] := SS5[k] - SD2[k];
        S5StringList_D2.Add(IntToStr(SD2[k]));
      end;
    end
    else
    begin
      S5StringList_D2.Add('0');
    end;
  end;

  // D3处理
  for k := 0 to CMPZSUM - 1 do
  begin
    if D3[k] > 0 then
    begin
      if SS1[k] - D3[k] >= 0 then
      begin
        SD3[k] := D3[k];
        D3[k] := 0;
        SS1[k] := SS1[k] - SD3[k];
        S1StringList_D3.Add(IntToStr(SD3[k]));
      end
      else
      begin
        SD3[k] := SS1[k];
        D3[k] := D3[k] - SD3[k];
        SS1[k] := SS1[k] - SD3[k];
        S1StringList_D3.Add(IntToStr(SD3[k]));
      end;
    end
    else
    begin
      S1StringList_D3.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D3[k] > 0 then
    begin
      if SS2[k] - D3[k] >= 0 then
      begin
        SD3[k] := D3[k];
        D3[k] := 0;
        SS2[k] := SS2[k] - SD3[k];
        S2StringList_D3.Add(IntToStr(SD3[k]));
      end
      else
      begin
        SD3[k] := SS2[k];
        D3[k] := D3[k] - SD3[k];
        SS2[k] := SS2[k] - SD3[k];
        S2StringList_D3.Add(IntToStr(SD3[k]));
      end;
    end
    else
    begin
      S2StringList_D3.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D3[k] > 0 then
    begin
      if SS3[k] - D3[k] >= 0 then
      begin
        SD3[k] := D3[k];
        D3[k] := 0;
        SS3[k] := SS3[k] - SD3[k];
        S3StringList_D3.Add(IntToStr(SD3[k]));
      end
      else
      begin
        SD3[k] := SS3[k];
        D3[k] := D3[k] - SD3[k];
        SS3[k] := SS3[k] - SD3[k];
        S3StringList_D3.Add(IntToStr(SD3[k]));
      end;
    end
    else
    begin
      S3StringList_D3.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D3[k] > 0 then
    begin
      if SS4[k] - D3[k] >= 0 then
      begin
        SD3[k] := D3[k];
        D3[k] := 0;
        SS4[k] := SS4[k] - SD3[k];
        S4StringList_D3.Add(IntToStr(SD3[k]));
      end
      else
      begin
        SD3[k] := SS4[k];
        D3[k] := D3[k] - SD3[k];
        SS4[k] := SS4[k] - SD3[k];
        S4StringList_D3.Add(IntToStr(SD3[k]));
      end;
    end
    else
    begin
      S4StringList_D3.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D3[k] > 0 then
    begin
      if SS5[k] - D3[k] >= 0 then
      begin
        SD3[k] := D3[k];
        D3[k] := 0;
        SS5[k] := SS5[k] - SD3[k];
        S5StringList_D3.Add(IntToStr(SD3[k]));
      end
      else
      begin
        SD3[k] := SS5[k];
        D3[k] := D3[k] - SD3[k];
        SS5[k] := SS5[k] - SD3[k];
        S5StringList_D3.Add(IntToStr(SD3[k]));
      end;
    end
    else
    begin
      S5StringList_D3.Add('0');
    end;
  end;

  // SS1, SS2, SS3, SS4, SS5: Array of Integer;
  // KS1, KS2, KS3, KS4, KS5: Array of Integer;
  // SSALL1, SSALL2, SSALL3, SSALL4, SSALL5: Array of Integer;

  SSALL1StringList := TStringList.Create;
  Kc1StringList := TStringList.Create;

  MATERIALFZID1 := SSALL1[13];
  cmtypenum1 := SSALL1[14];
  if MATERIALFZID1 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL1StringList.Add(IntToStr(SSALL1[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc1StringList.Add(IntToStr(KS1[k]));
    end;

    FillGrid2Detail(SSALL1StringList, Kc1StringList, S1StringList_D1,
      S1StringList_D2, S1StringList_D3, MATERIALFZID1, cmtypenum1);
  end;

  SSALL2StringList := TStringList.Create;
  Kc2StringList := TStringList.Create;

  MATERIALFZID2 := SSALL2[13];
  cmtypenum2 := SSALL2[14];
  if MATERIALFZID2 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL2StringList.Add(IntToStr(SSALL2[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc2StringList.Add(IntToStr(KS2[k]));
    end;

    FillGrid2Detail(SSALL2StringList, Kc2StringList, S2StringList_D1,
      S2StringList_D2, S2StringList_D3, MATERIALFZID2, cmtypenum2);

  end;

  SSALL3StringList := TStringList.Create;
  Kc3StringList := TStringList.Create;

  MATERIALFZID3 := SSALL3[13];
  cmtypenum3 := SSALL3[14];
  if MATERIALFZID3 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL3StringList.Add(IntToStr(SSALL3[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc3StringList.Add(IntToStr(KS3[k]));
    end;

    FillGrid2Detail(SSALL3StringList, Kc3StringList, S3StringList_D1,
      S3StringList_D2, S3StringList_D3, MATERIALFZID3, cmtypenum3);
  end;

  SSALL4StringList := TStringList.Create;
  Kc4StringList := TStringList.Create;

  MATERIALFZID4 := SSALL4[13];
  cmtypenum4 := SSALL4[14];
  if MATERIALFZID4 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL4StringList.Add(IntToStr(SSALL4[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc4StringList.Add(IntToStr(KS4[k]));
    end;

    FillGrid2Detail(SSALL4StringList, Kc4StringList, S4StringList_D1,
      S4StringList_D2, S4StringList_D3, MATERIALFZID4, cmtypenum4);
  end;

  SSALL5StringList := TStringList.Create;
  Kc5StringList := TStringList.Create;

  MATERIALFZID5 := SSALL5[13];
  cmtypenum5 := SSALL5[14];
  if MATERIALFZID5 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL5StringList.Add(IntToStr(SSALL5[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc5StringList.Add(IntToStr(KS5[k]));
    end;

    FillGrid2Detail(SSALL5StringList, Kc5StringList, S5StringList_D1,
      S5StringList_D2, S5StringList_D3, MATERIALFZID5, cmtypenum5);
  end;

  // G_PcAddNum := G_PcAddNum + 3;

end;

procedure TThreadPdCal.FillGrid2Detail(SAll, Kc, D1, D2, D3: TStringList;
  MATERIALFZID, cmtypenum: Integer);
var
  o: Integer;
  Sum1, Sum2, Sum3, Sum4: Integer;
  Sum_Kc, Sum_All: Integer;
  sequence: Integer;
  KcFlag: Boolean;
begin

  KcFlag := false;
  for o := 0 to CMPZSUM - 1 do
  begin
    if StrToInt(Kc[o]) > 0 then
    begin
      KcFlag := true;
      break;
    end;
  end;

  Sum1 := 0;
  Sum2 := 0;
  Sum3 := 0;
  Sum4 := 0;
  Sum_Kc := 0;
  Sum_All := 0;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := '小计';
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(SAll[o]);
      Sum1 := Sum1 + StrToInt(SAll[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(SAll[o]);
      Sum1 := Sum1 + StrToInt(SAll[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(SAll[o]);
      Sum1 := Sum1 + StrToInt(SAll[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum1;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  // 如果有库存，加入库存数据
  if KcFlag then
  begin
    DMUtilSelect.UniQuery1.Close;
    DMUtilSelect.UniQuery1.Sql.Clear;
    DMUtilSelect.UniQuery1.Sql.Add
      ('select nextval (''cc_materialdetail'') seq');
    DMUtilSelect.UniQuery1.ExecSQL;

    if not DMUtilSelect.UniQuery1.Eof then
    begin
      sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
    end;
    DMUtilSelect.UniQuery1.Close;

    DMUtilSave.UniQuery1.Close;
    DMUtilSave.UniQuery1.Sql.Clear;
    DMUtilSave.UniQuery1.Sql.Add
      ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
      + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
    DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
    DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
    DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := '减库存';
    DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

    if cmtypenum = 1 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
          StrToInt(Kc[o]);
        Sum_Kc := Sum_Kc + StrToInt(Kc[o]);
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    if cmtypenum = 2 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
          StrToInt(Kc[o]);
        Sum_Kc := Sum_Kc + StrToInt(Kc[o]);
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    if cmtypenum = 3 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
          StrToInt(Kc[o]);
        Sum_Kc := Sum_Kc + StrToInt(Kc[o]);
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum_Kc;

    DMUtilSave.UniQuery1.ExecSQL;
    DMUtilSave.UniQuery1.Close;
  end;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 1 + G_PcAddNum;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D1[o]);
      Sum2 := Sum2 + StrToInt(D1[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D1[o]);
      Sum2 := Sum2 + StrToInt(D1[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D1[o]);
      Sum2 := Sum2 + StrToInt(D1[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum2;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 2 + G_PcAddNum;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D2[o]);
      Sum3 := Sum3 + StrToInt(D2[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D2[o]);
      Sum3 := Sum3 + StrToInt(D2[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D2[o]);
      Sum3 := Sum3 + StrToInt(D2[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum3;
  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 3 + G_PcAddNum;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D3[o]);
      Sum4 := Sum4 + StrToInt(D3[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D3[o]);
      Sum4 := Sum4 + StrToInt(D3[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D3[o]);
      Sum4 := Sum4 + StrToInt(D3[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum4;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  // 插入空白行
  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 0;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value := 0;
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value := 0;
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value := 0;
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := 0;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('update cc_materialdetail set cjbc=:cjbc where MATERIALFZID=:MATERIALFZID');
  DMUtilSave.UniQuery1.ParamByName('cjbc').Value := G_bc;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('update cc_materialdetail set CJ_1=CM_1*(1+cjbc),CJ_2=CM_2*(1+cjbc),CJ_3=CM_3*(1+cjbc),CJ_4=CM_4*(1+cjbc),CJ_5=CM_5*(1+cjbc),CJ_6=CM_6*(1+cjbc),CJ_7=CM_7*(1+cjbc),CJ_8=CM_8*(1+cjbc),CJ_9=CM_9*(1+cjbc),CJ_10=CM_10*(1+cjbc),'
    + ' CJ_11=CM_11*(1+cjbc),CJ_12=CM_12*(1+cjbc),CJ_13=CM_13*(1+cjbc),CJ_14=CM_14*(1+cjbc),CJ_15=CM_15*(1+cjbc),CJ_Sum=Cm_Sum*(1+cjbc) where (CJ_Sum is null or CJ_Sum =0)');
  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

end;

procedure TThreadPdCal.FillGrid2Five(S, D1StringList, D2StringList,
  D3StringList, D4StringList, D5StringList: TStringList);
var
  S1StringList_D1, S1StringList_D2, S1StringList_D3, S1StringList_D4,
    S1StringList_D5: TStringList;
  S2StringList_D1, S2StringList_D2, S2StringList_D3, S2StringList_D4,
    S2StringList_D5: TStringList;
  S3StringList_D1, S3StringList_D2, S3StringList_D3, S3StringList_D4,
    S3StringList_D5: TStringList;
  S4StringList_D1, S4StringList_D2, S4StringList_D3, S4StringList_D4,
    S4StringList_D5: TStringList;
  S5StringList_D1, S5StringList_D2, S5StringList_D3, S5StringList_D4,
    S5StringList_D5: TStringList;
  SSALL1StringList, SSALL2StringList, SSALL3StringList, SSALL4StringList,
    SSALL5StringList: TStringList;
  Kc1StringList, Kc2StringList, Kc3StringList, Kc4StringList,
    Kc5StringList: TStringList;
  k: Integer;
  SD1, SD2, SD3, SD4, SD5: Array of Integer;
  D1, D2, D3, D4, D5: Array of Integer;
  MATERIALFZID1, MATERIALFZID2, MATERIALFZID3, MATERIALFZID4,
    MATERIALFZID5: Integer;
  cmtypenum1, cmtypenum2, cmtypenum3, cmtypenum4, cmtypenum5: Integer;
begin
  S1StringList_D1 := TStringList.Create;
  S1StringList_D2 := TStringList.Create;
  S1StringList_D3 := TStringList.Create;
  S1StringList_D4 := TStringList.Create;
  S1StringList_D5 := TStringList.Create;

  S2StringList_D1 := TStringList.Create;
  S2StringList_D2 := TStringList.Create;
  S2StringList_D3 := TStringList.Create;
  S2StringList_D4 := TStringList.Create;
  S2StringList_D5 := TStringList.Create;

  S3StringList_D1 := TStringList.Create;
  S3StringList_D2 := TStringList.Create;
  S3StringList_D3 := TStringList.Create;
  S3StringList_D4 := TStringList.Create;
  S3StringList_D5 := TStringList.Create;

  S4StringList_D1 := TStringList.Create;
  S4StringList_D2 := TStringList.Create;
  S4StringList_D3 := TStringList.Create;
  S4StringList_D4 := TStringList.Create;
  S4StringList_D5 := TStringList.Create;

  S5StringList_D1 := TStringList.Create;
  S5StringList_D2 := TStringList.Create;
  S5StringList_D3 := TStringList.Create;
  S5StringList_D4 := TStringList.Create;
  S5StringList_D5 := TStringList.Create;

  setlength(SD1, CMPZSUM);
  setlength(SD2, CMPZSUM);
  setlength(SD3, CMPZSUM);
  setlength(SD4, CMPZSUM);
  setlength(SD5, CMPZSUM);

  setlength(D1, CMPZSUM);
  D1[0] := StrToInt(D1StringList.Strings[0]);
  D1[1] := StrToInt(D1StringList.Strings[1]);
  D1[2] := StrToInt(D1StringList.Strings[2]);
  D1[3] := StrToInt(D1StringList.Strings[3]);
  D1[4] := StrToInt(D1StringList.Strings[4]);
  D1[5] := StrToInt(D1StringList.Strings[5]);
  D1[6] := StrToInt(D1StringList.Strings[6]);
  D1[7] := StrToInt(D1StringList.Strings[7]);
  D1[8] := StrToInt(D1StringList.Strings[8]);
  D1[9] := StrToInt(D1StringList.Strings[9]);
  D1[10] := StrToInt(D1StringList.Strings[10]);
  D1[11] := StrToInt(D1StringList.Strings[11]);
  D1[12] := StrToInt(D1StringList.Strings[12]);

  setlength(D2, CMPZSUM);
  D2[0] := StrToInt(D2StringList.Strings[0]);
  D2[1] := StrToInt(D2StringList.Strings[1]);
  D2[2] := StrToInt(D2StringList.Strings[2]);
  D2[3] := StrToInt(D2StringList.Strings[3]);
  D2[4] := StrToInt(D2StringList.Strings[4]);
  D2[5] := StrToInt(D2StringList.Strings[5]);
  D2[6] := StrToInt(D2StringList.Strings[6]);
  D2[7] := StrToInt(D2StringList.Strings[7]);
  D2[8] := StrToInt(D2StringList.Strings[8]);
  D2[9] := StrToInt(D2StringList.Strings[9]);
  D2[10] := StrToInt(D2StringList.Strings[10]);
  D2[11] := StrToInt(D2StringList.Strings[11]);
  D2[12] := StrToInt(D2StringList.Strings[12]);

  setlength(D3, CMPZSUM);
  D3[0] := StrToInt(D3StringList.Strings[0]);
  D3[1] := StrToInt(D3StringList.Strings[1]);
  D3[2] := StrToInt(D3StringList.Strings[2]);
  D3[3] := StrToInt(D3StringList.Strings[3]);
  D3[4] := StrToInt(D3StringList.Strings[4]);
  D3[5] := StrToInt(D3StringList.Strings[5]);
  D3[6] := StrToInt(D3StringList.Strings[6]);
  D3[7] := StrToInt(D3StringList.Strings[7]);
  D3[8] := StrToInt(D3StringList.Strings[8]);
  D3[9] := StrToInt(D3StringList.Strings[9]);
  D3[10] := StrToInt(D3StringList.Strings[10]);
  D3[11] := StrToInt(D3StringList.Strings[11]);
  D3[12] := StrToInt(D3StringList.Strings[12]);

  setlength(D4, CMPZSUM);
  D4[0] := StrToInt(D4StringList.Strings[0]);
  D4[1] := StrToInt(D4StringList.Strings[1]);
  D4[2] := StrToInt(D4StringList.Strings[2]);
  D4[3] := StrToInt(D4StringList.Strings[3]);
  D4[4] := StrToInt(D4StringList.Strings[4]);
  D4[5] := StrToInt(D4StringList.Strings[5]);
  D4[6] := StrToInt(D4StringList.Strings[6]);
  D4[7] := StrToInt(D4StringList.Strings[7]);
  D4[8] := StrToInt(D4StringList.Strings[8]);
  D4[9] := StrToInt(D4StringList.Strings[9]);
  D4[10] := StrToInt(D4StringList.Strings[10]);
  D4[11] := StrToInt(D4StringList.Strings[11]);
  D4[12] := StrToInt(D4StringList.Strings[12]);

  setlength(D5, CMPZSUM);
  D5[0] := StrToInt(D5StringList.Strings[0]);
  D5[1] := StrToInt(D5StringList.Strings[1]);
  D5[2] := StrToInt(D5StringList.Strings[2]);
  D5[3] := StrToInt(D5StringList.Strings[3]);
  D5[4] := StrToInt(D5StringList.Strings[4]);
  D5[5] := StrToInt(D5StringList.Strings[5]);
  D5[6] := StrToInt(D5StringList.Strings[6]);
  D5[7] := StrToInt(D5StringList.Strings[7]);
  D5[8] := StrToInt(D5StringList.Strings[8]);
  D5[9] := StrToInt(D5StringList.Strings[9]);
  D5[10] := StrToInt(D5StringList.Strings[10]);
  D5[11] := StrToInt(D5StringList.Strings[11]);
  D5[12] := StrToInt(D5StringList.Strings[12]);

  // D1处理
  for k := 0 to CMPZSUM - 1 do
  begin
    if D1[k] > 0 then
    begin
      if SS1[k] - D1[k] >= 0 then
      begin
        SD1[k] := D1[k];
        D1[k] := 0;
        SS1[k] := SS1[k] - SD1[k];
        S1StringList_D1.Add(IntToStr(SD1[k]));
      end
      else
      begin
        SD1[k] := SS1[k];
        D1[k] := D1[k] - SD1[k];
        SS1[k] := SS1[k] - SD1[k];
        S1StringList_D1.Add(IntToStr(SD1[k]));
      end;
    end
    else
    begin
      S1StringList_D1.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D1[k] > 0 then
    begin
      if SS2[k] - D1[k] >= 0 then
      begin
        SD1[k] := D1[k];
        D1[k] := 0;
        SS2[k] := SS2[k] - SD1[k];
        S2StringList_D1.Add(IntToStr(SD1[k]));
      end
      else
      begin
        SD1[k] := SS2[k];
        D1[k] := D1[k] - SD1[k];
        SS2[k] := SS2[k] - SD1[k];
        S2StringList_D1.Add(IntToStr(SD1[k]));
      end;
    end
    else
    begin
      S2StringList_D1.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D1[k] > 0 then
    begin
      if SS3[k] - D1[k] >= 0 then
      begin
        SD1[k] := D1[k];
        D1[k] := 0;
        SS3[k] := SS3[k] - SD1[k];
        S3StringList_D1.Add(IntToStr(SD1[k]));
      end
      else
      begin
        SD1[k] := SS3[k];
        D1[k] := D1[k] - SD1[k];
        SS3[k] := SS3[k] - SD1[k];
        S3StringList_D1.Add(IntToStr(SD1[k]));
      end;
    end
    else
    begin
      S3StringList_D1.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D1[k] > 0 then
    begin
      if SS4[k] - D1[k] >= 0 then
      begin
        SD1[k] := D1[k];
        D1[k] := 0;
        SS4[k] := SS4[k] - SD1[k];
        S4StringList_D1.Add(IntToStr(SD1[k]));
      end
      else
      begin
        SD1[k] := SS4[k];
        D1[k] := D1[k] - SD1[k];
        SS4[k] := SS4[k] - SD1[k];
        S4StringList_D1.Add(IntToStr(SD1[k]));
      end;
    end
    else
    begin
      S4StringList_D1.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D1[k] > 0 then
    begin
      if SS5[k] - D1[k] >= 0 then
      begin
        SD1[k] := D1[k];
        D1[k] := 0;
        SS5[k] := SS5[k] - SD1[k];
        S5StringList_D1.Add(IntToStr(SD1[k]));
      end
      else
      begin
        SD1[k] := SS5[k];
        D1[k] := D1[k] - SD1[k];
        SS5[k] := SS5[k] - SD1[k];
        S5StringList_D1.Add(IntToStr(SD1[k]));
      end;
    end
    else
    begin
      S5StringList_D1.Add('0');
    end;
  end;

  // D2处理
  for k := 0 to CMPZSUM - 1 do
  begin
    if D2[k] > 0 then
    begin
      if SS1[k] - D2[k] >= 0 then
      begin
        SD2[k] := D2[k];
        D2[k] := 0;
        SS1[k] := SS1[k] - SD2[k];
        S1StringList_D2.Add(IntToStr(SD2[k]));
      end
      else
      begin
        SD2[k] := SS1[k];
        D2[k] := D2[k] - SD2[k];
        SS1[k] := SS1[k] - SD2[k];
        S1StringList_D2.Add(IntToStr(SD2[k]));
      end;
    end
    else
    begin
      S1StringList_D2.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D2[k] > 0 then
    begin
      if SS2[k] - D2[k] >= 0 then
      begin
        SD2[k] := D2[k];
        D2[k] := 0;
        SS2[k] := SS2[k] - SD2[k];
        S2StringList_D2.Add(IntToStr(SD2[k]));
      end
      else
      begin
        SD2[k] := SS2[k];
        D2[k] := D2[k] - SD2[k];
        SS2[k] := SS2[k] - SD2[k];
        S2StringList_D2.Add(IntToStr(SD2[k]));
      end;
    end
    else
    begin
      S2StringList_D2.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D2[k] > 0 then
    begin
      if SS3[k] - D2[k] >= 0 then
      begin
        SD2[k] := D2[k];
        D2[k] := 0;
        SS3[k] := SS3[k] - SD2[k];
        S3StringList_D2.Add(IntToStr(SD2[k]));
      end
      else
      begin
        SD2[k] := SS3[k];
        D2[k] := D2[k] - SD2[k];
        SS3[k] := SS3[k] - SD2[k];
        S3StringList_D2.Add(IntToStr(SD2[k]));
      end;
    end
    else
    begin
      S3StringList_D2.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D2[k] > 0 then
    begin
      if SS4[k] - D2[k] >= 0 then
      begin
        SD2[k] := D2[k];
        D2[k] := 0;
        SS4[k] := SS4[k] - SD2[k];
        S4StringList_D2.Add(IntToStr(SD2[k]));
      end
      else
      begin
        SD2[k] := SS4[k];
        D2[k] := D2[k] - SD2[k];
        SS4[k] := SS4[k] - SD2[k];
        S4StringList_D2.Add(IntToStr(SD2[k]));
      end;
    end
    else
    begin
      S4StringList_D2.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D2[k] > 0 then
    begin
      if SS5[k] - D2[k] >= 0 then
      begin
        SD2[k] := D2[k];
        D2[k] := 0;
        SS5[k] := SS5[k] - SD2[k];
        S5StringList_D2.Add(IntToStr(SD2[k]));
      end
      else
      begin
        SD2[k] := SS5[k];
        D2[k] := D2[k] - SD2[k];
        SS5[k] := SS5[k] - SD2[k];
        S5StringList_D2.Add(IntToStr(SD2[k]));
      end;
    end
    else
    begin
      S5StringList_D2.Add('0');
    end;
  end;

  // D3处理
  for k := 0 to CMPZSUM - 1 do
  begin
    if D3[k] > 0 then
    begin
      if SS1[k] - D3[k] >= 0 then
      begin
        SD3[k] := D3[k];
        D3[k] := 0;
        SS1[k] := SS1[k] - SD3[k];
        S1StringList_D3.Add(IntToStr(SD3[k]));
      end
      else
      begin
        SD3[k] := SS1[k];
        D3[k] := D3[k] - SD3[k];
        SS1[k] := SS1[k] - SD3[k];
        S1StringList_D3.Add(IntToStr(SD3[k]));
      end;
    end
    else
    begin
      S1StringList_D3.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D3[k] > 0 then
    begin
      if SS2[k] - D3[k] >= 0 then
      begin
        SD3[k] := D3[k];
        D3[k] := 0;
        SS2[k] := SS2[k] - SD3[k];
        S2StringList_D3.Add(IntToStr(SD3[k]));
      end
      else
      begin
        SD3[k] := SS2[k];
        D3[k] := D3[k] - SD3[k];
        SS2[k] := SS2[k] - SD3[k];
        S2StringList_D3.Add(IntToStr(SD3[k]));
      end;
    end
    else
    begin
      S2StringList_D3.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D3[k] > 0 then
    begin
      if SS3[k] - D3[k] >= 0 then
      begin
        SD3[k] := D3[k];
        D3[k] := 0;
        SS3[k] := SS3[k] - SD3[k];
        S3StringList_D3.Add(IntToStr(SD3[k]));
      end
      else
      begin
        SD3[k] := SS3[k];
        D3[k] := D3[k] - SD3[k];
        SS3[k] := SS3[k] - SD3[k];
        S3StringList_D3.Add(IntToStr(SD3[k]));
      end;
    end
    else
    begin
      S3StringList_D3.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D3[k] > 0 then
    begin
      if SS4[k] - D3[k] >= 0 then
      begin
        SD3[k] := D3[k];
        D3[k] := 0;
        SS4[k] := SS4[k] - SD3[k];
        S4StringList_D3.Add(IntToStr(SD3[k]));
      end
      else
      begin
        SD3[k] := SS4[k];
        D3[k] := D3[k] - SD3[k];
        SS4[k] := SS4[k] - SD3[k];
        S4StringList_D3.Add(IntToStr(SD3[k]));
      end;
    end
    else
    begin
      S4StringList_D3.Add('0');
    end;
  end;
  for k := 0 to CMPZSUM - 1 do
  begin
    if D3[k] > 0 then
    begin
      if SS5[k] - D3[k] >= 0 then
      begin
        SD3[k] := D3[k];
        D3[k] := 0;
        SS5[k] := SS5[k] - SD3[k];
        S5StringList_D3.Add(IntToStr(SD3[k]));
      end
      else
      begin
        SD3[k] := SS5[k];
        D3[k] := D3[k] - SD3[k];
        SS5[k] := SS5[k] - SD3[k];
        S5StringList_D3.Add(IntToStr(SD3[k]));
      end;
    end
    else
    begin
      S5StringList_D3.Add('0');
    end;
  end;

  // D4处理
  for k := 0 to CMPZSUM - 1 do
  begin
    if D4[k] > 0 then
    begin
      if SS1[k] - D4[k] >= 0 then
      begin
        SD4[k] := D4[k];
        D4[k] := 0;
        SS1[k] := SS1[k] - SD4[k];
        S1StringList_D4.Add(IntToStr(SD4[k]));
      end
      else
      begin
        SD4[k] := SS1[k];
        D4[k] := D4[k] - SD4[k];
        SS1[k] := SS1[k] - SD4[k];
        S1StringList_D4.Add(IntToStr(SD4[k]));
      end;
    end
    else
    begin
      S1StringList_D4.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D4[k] > 0 then
    begin
      if SS2[k] - D4[k] >= 0 then
      begin
        SD4[k] := D4[k];
        D4[k] := 0;
        SS2[k] := SS2[k] - SD4[k];
        S2StringList_D4.Add(IntToStr(SD4[k]));
      end
      else
      begin
        SD4[k] := SS2[k];
        D4[k] := D4[k] - SD4[k];
        SS2[k] := SS2[k] - SD4[k];
        S2StringList_D4.Add(IntToStr(SD4[k]));
      end;
    end
    else
    begin
      S2StringList_D4.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D4[k] > 0 then
    begin
      if SS3[k] - D4[k] >= 0 then
      begin
        SD4[k] := D4[k];
        D4[k] := 0;
        SS3[k] := SS3[k] - SD4[k];
        S3StringList_D4.Add(IntToStr(SD4[k]));
      end
      else
      begin
        SD4[k] := SS4[k];
        D4[k] := D4[k] - SD4[k];
        SS3[k] := SS3[k] - SD4[k];
        S3StringList_D4.Add(IntToStr(SD4[k]));
      end;
    end
    else
    begin
      S3StringList_D4.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D4[k] > 0 then
    begin
      if SS4[k] - D4[k] >= 0 then
      begin
        SD4[k] := D4[k];
        D4[k] := 0;
        SS4[k] := SS4[k] - SD4[k];
        S4StringList_D4.Add(IntToStr(SD4[k]));
      end
      else
      begin
        SD4[k] := SS4[k];
        D4[k] := D4[k] - SD4[k];
        SS4[k] := SS4[k] - SD4[k];
        S4StringList_D4.Add(IntToStr(SD4[k]));
      end;
    end
    else
    begin
      S4StringList_D4.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D5[k] > 0 then
    begin
      if SS5[k] - D4[k] >= 0 then
      begin
        SD4[k] := D4[k];
        D4[k] := 0;
        SS5[k] := SS5[k] - SD4[k];
        S5StringList_D4.Add(IntToStr(SD4[k]));
      end
      else
      begin
        SD5[k] := SS5[k];
        D5[k] := D4[k] - SD4[k];
        SS5[k] := SS5[k] - SD4[k];
        S5StringList_D4.Add(IntToStr(SD4[k]));
      end;
    end
    else
    begin
      S5StringList_D4.Add('0');
    end;
  end;

  // D5处理
  for k := 0 to CMPZSUM - 1 do
  begin
    if D5[k] > 0 then
    begin
      if SS1[k] - D5[k] >= 0 then
      begin
        SD5[k] := D5[k];
        D5[k] := 0;
        SS1[k] := SS1[k] - SD5[k];
        S1StringList_D5.Add(IntToStr(SD5[k]));
      end
      else
      begin
        SD5[k] := SS1[k];
        D5[k] := D5[k] - SD5[k];
        SS1[k] := SS1[k] - SD5[k];
        S1StringList_D5.Add(IntToStr(SD5[k]));
      end;
    end
    else
    begin
      S1StringList_D5.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D5[k] > 0 then
    begin
      if SS2[k] - D5[k] >= 0 then
      begin
        SD5[k] := D5[k];
        D5[k] := 0;
        SS2[k] := SS2[k] - SD5[k];
        S2StringList_D5.Add(IntToStr(SD5[k]));
      end
      else
      begin
        SD5[k] := SS2[k];
        D5[k] := D5[k] - SD5[k];
        SS2[k] := SS2[k] - SD5[k];
        S2StringList_D5.Add(IntToStr(SD5[k]));
      end;
    end
    else
    begin
      S2StringList_D5.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D5[k] > 0 then
    begin
      if SS3[k] - D5[k] >= 0 then
      begin
        SD5[k] := D5[k];
        D5[k] := 0;
        SS3[k] := SS3[k] - SD5[k];
        S3StringList_D5.Add(IntToStr(SD5[k]));
      end
      else
      begin
        SD5[k] := SS3[k];
        D5[k] := D5[k] - SD5[k];
        SS3[k] := SS3[k] - SD5[k];
        S3StringList_D5.Add(IntToStr(SD5[k]));
      end;
    end
    else
    begin
      S3StringList_D5.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D5[k] > 0 then
    begin
      if SS4[k] - D5[k] >= 0 then
      begin
        SD5[k] := D5[k];
        D5[k] := 0;
        SS4[k] := SS4[k] - SD5[k];
        S4StringList_D5.Add(IntToStr(SD5[k]));
      end
      else
      begin
        SD5[k] := SS4[k];
        D5[k] := D5[k] - SD5[k];
        SS4[k] := SS4[k] - SD5[k];
        S4StringList_D5.Add(IntToStr(SD5[k]));
      end;
    end
    else
    begin
      S4StringList_D5.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D5[k] > 0 then
    begin
      if SS5[k] - D5[k] >= 0 then
      begin
        SD5[k] := D5[k];
        D5[k] := 0;
        SS5[k] := SS5[k] - SD5[k];
        S5StringList_D5.Add(IntToStr(SD5[k]));
      end
      else
      begin
        SD5[k] := SS5[k];
        D5[k] := D5[k] - SD5[k];
        SS5[k] := SS5[k] - SD5[k];
        S5StringList_D5.Add(IntToStr(SD5[k]));
      end;
    end
    else
    begin
      S5StringList_D5.Add('0');
    end;
  end;

  // SS1, SS2, SS3, SS4, SS5: Array of Integer;
  // KS1, KS2, KS3, KS4, KS5: Array of Integer;
  // SSALL1, SSALL2, SSALL3, SSALL4, SSALL5: Array of Integer;

  SSALL1StringList := TStringList.Create;
  Kc1StringList := TStringList.Create;

  MATERIALFZID1 := SSALL1[13];
  cmtypenum1 := SSALL1[14];
  if MATERIALFZID1 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL1StringList.Add(IntToStr(SSALL1[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc1StringList.Add(IntToStr(KS1[k]));
    end;

    FillGrid2FiveDetail(SSALL1StringList, Kc1StringList, S1StringList_D1,
      S1StringList_D2, S1StringList_D3, S1StringList_D4, S1StringList_D5,
      MATERIALFZID1, cmtypenum1);
  end;

  SSALL2StringList := TStringList.Create;
  Kc2StringList := TStringList.Create;

  MATERIALFZID2 := SSALL2[13];
  cmtypenum2 := SSALL2[14];
  if MATERIALFZID2 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL2StringList.Add(IntToStr(SSALL2[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc2StringList.Add(IntToStr(KS2[k]));
    end;

    FillGrid2FiveDetail(SSALL2StringList, Kc2StringList, S2StringList_D1,
      S2StringList_D2, S2StringList_D3, S2StringList_D4, S2StringList_D5,
      MATERIALFZID2, cmtypenum2);
  end;

  SSALL3StringList := TStringList.Create;
  Kc3StringList := TStringList.Create;

  MATERIALFZID3 := SSALL3[13];
  cmtypenum3 := SSALL3[14];
  if MATERIALFZID3 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL3StringList.Add(IntToStr(SSALL3[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc3StringList.Add(IntToStr(KS3[k]));
    end;

    FillGrid2FiveDetail(SSALL3StringList, Kc3StringList, S3StringList_D1,
      S3StringList_D2, S3StringList_D3, S3StringList_D4, S3StringList_D5,
      MATERIALFZID3, cmtypenum3);
  end;

  SSALL4StringList := TStringList.Create;
  Kc4StringList := TStringList.Create;

  MATERIALFZID4 := SSALL4[13];
  cmtypenum4 := SSALL4[14];
  if MATERIALFZID4 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL4StringList.Add(IntToStr(SSALL4[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc4StringList.Add(IntToStr(KS4[k]));
    end;

    FillGrid2FiveDetail(SSALL4StringList, Kc4StringList, S4StringList_D1,
      S4StringList_D2, S4StringList_D3, S4StringList_D4, S4StringList_D5,
      MATERIALFZID4, cmtypenum4);
  end;

  SSALL5StringList := TStringList.Create;
  Kc5StringList := TStringList.Create;

  MATERIALFZID5 := SSALL5[13];
  cmtypenum5 := SSALL5[14];
  if MATERIALFZID5 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL5StringList.Add(IntToStr(SSALL5[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc5StringList.Add(IntToStr(KS5[k]));
    end;

    FillGrid2FiveDetail(SSALL5StringList, Kc5StringList, S5StringList_D1,
      S5StringList_D2, S5StringList_D3, S5StringList_D4, S5StringList_D5,
      MATERIALFZID5, cmtypenum5);
  end;

  // G_PcAddNum := G_PcAddNum + 5;

end;

procedure TThreadPdCal.FillGrid2FiveDetail(SAll, Kc, D1, D2, D3, D4,
  D5: TStringList; MATERIALFZID, cmtypenum: Integer);
var
  o: Integer;
  Sum1, Sum2, Sum3, Sum4, Sum5, Sum6: Integer;
  Sum_Kc, Sum_All: Integer;
  sequence: Integer;
  KcFlag: Boolean;
begin

  KcFlag := false;
  for o := 0 to CMPZSUM - 1 do
  begin
    if StrToInt(Kc[o]) > 0 then
    begin
      KcFlag := true;
      break;
    end;
  end;

  Sum1 := 0;
  Sum2 := 0;
  Sum3 := 0;
  Sum4 := 0;
  Sum5 := 0;
  Sum6 := 0;

  Sum_Kc := 0;
  Sum_All := 0;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := '小计';
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(SAll[o]);
      Sum1 := Sum1 + StrToInt(SAll[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(SAll[o]);
      Sum1 := Sum1 + StrToInt(SAll[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(SAll[o]);
      Sum1 := Sum1 + StrToInt(SAll[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum1;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  // 如果有库存，加入库存数据
  if KcFlag then
  begin
    DMUtilSelect.UniQuery1.Close;
    DMUtilSelect.UniQuery1.Sql.Clear;
    DMUtilSelect.UniQuery1.Sql.Add
      ('select nextval (''cc_materialdetail'') seq');
    DMUtilSelect.UniQuery1.ExecSQL;

    if not DMUtilSelect.UniQuery1.Eof then
    begin
      sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
    end;
    DMUtilSelect.UniQuery1.Close;

    DMUtilSave.UniQuery1.Close;
    DMUtilSave.UniQuery1.Sql.Clear;
    DMUtilSave.UniQuery1.Sql.Add
      ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
      + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
    DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
    DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
    DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := '减库存';
    DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

    if cmtypenum = 1 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
          StrToInt(Kc[o]);
        Sum_Kc := Sum_Kc + StrToInt(Kc[o]);
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    if cmtypenum = 2 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
          StrToInt(Kc[o]);
        Sum_Kc := Sum_Kc + StrToInt(Kc[o]);
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    if cmtypenum = 3 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
          StrToInt(Kc[o]);
        Sum_Kc := Sum_Kc + StrToInt(Kc[o]);
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum_Kc;

    DMUtilSave.UniQuery1.ExecSQL;
    DMUtilSave.UniQuery1.Close;
  end;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 1 + G_PcAddNum;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D1[o]);
      Sum2 := Sum2 + StrToInt(D1[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D1[o]);
      Sum2 := Sum2 + StrToInt(D1[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D1[o]);
      Sum2 := Sum2 + StrToInt(D1[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum2;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 2 + G_PcAddNum;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D2[o]);
      Sum3 := Sum3 + StrToInt(D2[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D2[o]);
      Sum3 := Sum3 + StrToInt(D2[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D2[o]);
      Sum3 := Sum3 + StrToInt(D2[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum3;
  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 3 + G_PcAddNum;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D3[o]);
      Sum4 := Sum4 + StrToInt(D3[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D3[o]);
      Sum4 := Sum4 + StrToInt(D3[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D3[o]);
      Sum4 := Sum4 + StrToInt(D3[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum4;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 4 + G_PcAddNum;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D4[o]);
      Sum5 := Sum5 + StrToInt(D4[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D4[o]);
      Sum5 := Sum5 + StrToInt(D4[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D4[o]);
      Sum5 := Sum5 + StrToInt(D4[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum5;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 5 + G_PcAddNum;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D5[o]);
      Sum6 := Sum6 + StrToInt(D5[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D5[o]);
      Sum6 := Sum6 + StrToInt(D5[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D5[o]);
      Sum6 := Sum6 + StrToInt(D5[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum6;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  // 插入空白行
  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 0;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value := 0;
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value := 0;
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value := 0;
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := 0;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  // 根据MATERIALFZID 取得对应的备次基础数据，进行数据更新

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('update cc_materialdetail set cjbc=:cjbc where MATERIALFZID=:MATERIALFZID');
  DMUtilSave.UniQuery1.ParamByName('cjbc').Value := G_bc;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('update cc_materialdetail set CJ_1=CM_1*(1+cjbc),CJ_2=CM_2*(1+cjbc),CJ_3=CM_3*(1+cjbc),CJ_4=CM_4*(1+cjbc),CJ_5=CM_5*(1+cjbc),CJ_6=CM_6*(1+cjbc),CJ_7=CM_7*(1+cjbc),CJ_8=CM_8*(1+cjbc),CJ_9=CM_9*(1+cjbc),CJ_10=CM_10*(1+cjbc),'
    + ' CJ_11=CM_11*(1+cjbc),CJ_12=CM_12*(1+cjbc),CJ_13=CM_13*(1+cjbc),CJ_14=CM_14*(1+cjbc),CJ_15=CM_15*(1+cjbc),CJ_Sum=Cm_Sum*(1+cjbc) where (CJ_Sum is null or CJ_Sum =0)');
  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

end;

procedure TThreadPdCal.FillGrid2Ten(S, D1StringList, D2StringList, D3StringList,
  D4StringList, D5StringList, D6StringList, D7StringList, D8StringList,
  D9StringList, D10StringList: TStringList);
var
  S1StringList_D1, S1StringList_D2, S1StringList_D3, S1StringList_D4,
    S1StringList_D5, S1StringList_D6, S1StringList_D7, S1StringList_D8,
    S1StringList_D9, S1StringList_D10: TStringList;
  S2StringList_D1, S2StringList_D2, S2StringList_D3, S2StringList_D4,
    S2StringList_D5, S2StringList_D6, S2StringList_D7, S2StringList_D8,
    S2StringList_D9, S2StringList_D10: TStringList;
  S3StringList_D1, S3StringList_D2, S3StringList_D3, S3StringList_D4,
    S3StringList_D5, S3StringList_D6, S3StringList_D7, S3StringList_D8,
    S3StringList_D9, S3StringList_D10: TStringList;
  S4StringList_D1, S4StringList_D2, S4StringList_D3, S4StringList_D4,
    S4StringList_D5, S4StringList_D6, S4StringList_D7, S4StringList_D8,
    S4StringList_D9, S4StringList_D10: TStringList;
  S5StringList_D1, S5StringList_D2, S5StringList_D3, S5StringList_D4,
    S5StringList_D5, S5StringList_D6, S5StringList_D7, S5StringList_D8,
    S5StringList_D9, S5StringList_D10: TStringList;
  SSALL1StringList, SSALL2StringList, SSALL3StringList, SSALL4StringList,
    SSALL5StringList: TStringList;
  Kc1StringList, Kc2StringList, Kc3StringList, Kc4StringList,
    Kc5StringList: TStringList;
  k: Integer;
  SD1, SD2, SD3, SD4, SD5, SD6, SD7, SD8, SD9, SD10: Array of Integer;
  D1, D2, D3, D4, D5, D6, D7, D8, D9, D10: Array of Integer;
  MATERIALFZID1, MATERIALFZID2, MATERIALFZID3, MATERIALFZID4,
    MATERIALFZID5: Integer;
  cmtypenum1, cmtypenum2, cmtypenum3, cmtypenum4, cmtypenum5: Integer;
begin
  S1StringList_D1 := TStringList.Create;
  S1StringList_D2 := TStringList.Create;
  S1StringList_D3 := TStringList.Create;
  S1StringList_D4 := TStringList.Create;
  S1StringList_D5 := TStringList.Create;
  S1StringList_D6 := TStringList.Create;
  S1StringList_D7 := TStringList.Create;
  S1StringList_D8 := TStringList.Create;
  S1StringList_D9 := TStringList.Create;
  S1StringList_D10 := TStringList.Create;

  S2StringList_D1 := TStringList.Create;
  S2StringList_D2 := TStringList.Create;
  S2StringList_D3 := TStringList.Create;
  S2StringList_D4 := TStringList.Create;
  S2StringList_D5 := TStringList.Create;
  S2StringList_D6 := TStringList.Create;
  S2StringList_D7 := TStringList.Create;
  S2StringList_D8 := TStringList.Create;
  S2StringList_D9 := TStringList.Create;
  S2StringList_D10 := TStringList.Create;

  S3StringList_D1 := TStringList.Create;
  S3StringList_D2 := TStringList.Create;
  S3StringList_D3 := TStringList.Create;
  S3StringList_D4 := TStringList.Create;
  S3StringList_D5 := TStringList.Create;
  S3StringList_D6 := TStringList.Create;
  S3StringList_D7 := TStringList.Create;
  S3StringList_D8 := TStringList.Create;
  S3StringList_D9 := TStringList.Create;
  S3StringList_D10 := TStringList.Create;

  S4StringList_D1 := TStringList.Create;
  S4StringList_D2 := TStringList.Create;
  S4StringList_D3 := TStringList.Create;
  S4StringList_D4 := TStringList.Create;
  S4StringList_D5 := TStringList.Create;
  S4StringList_D6 := TStringList.Create;
  S4StringList_D7 := TStringList.Create;
  S4StringList_D8 := TStringList.Create;
  S4StringList_D9 := TStringList.Create;
  S4StringList_D10 := TStringList.Create;

  S5StringList_D1 := TStringList.Create;
  S5StringList_D2 := TStringList.Create;
  S5StringList_D3 := TStringList.Create;
  S5StringList_D4 := TStringList.Create;
  S5StringList_D5 := TStringList.Create;
  S5StringList_D6 := TStringList.Create;
  S5StringList_D7 := TStringList.Create;
  S5StringList_D8 := TStringList.Create;
  S5StringList_D9 := TStringList.Create;
  S5StringList_D10 := TStringList.Create;

  setlength(SD1, CMPZSUM);
  setlength(SD2, CMPZSUM);
  setlength(SD3, CMPZSUM);
  setlength(SD4, CMPZSUM);
  setlength(SD5, CMPZSUM);
  setlength(SD6, CMPZSUM);
  setlength(SD7, CMPZSUM);
  setlength(SD8, CMPZSUM);
  setlength(SD9, CMPZSUM);
  setlength(SD10, CMPZSUM);

  setlength(D1, CMPZSUM);
  D1[0] := StrToInt(D1StringList.Strings[0]);
  D1[1] := StrToInt(D1StringList.Strings[1]);
  D1[2] := StrToInt(D1StringList.Strings[2]);
  D1[3] := StrToInt(D1StringList.Strings[3]);
  D1[4] := StrToInt(D1StringList.Strings[4]);
  D1[5] := StrToInt(D1StringList.Strings[5]);
  D1[6] := StrToInt(D1StringList.Strings[6]);
  D1[7] := StrToInt(D1StringList.Strings[7]);
  D1[8] := StrToInt(D1StringList.Strings[8]);
  D1[9] := StrToInt(D1StringList.Strings[9]);
  D1[10] := StrToInt(D1StringList.Strings[10]);
  D1[11] := StrToInt(D1StringList.Strings[11]);
  D1[12] := StrToInt(D1StringList.Strings[12]);

  setlength(D2, CMPZSUM);
  D2[0] := StrToInt(D2StringList.Strings[0]);
  D2[1] := StrToInt(D2StringList.Strings[1]);
  D2[2] := StrToInt(D2StringList.Strings[2]);
  D2[3] := StrToInt(D2StringList.Strings[3]);
  D2[4] := StrToInt(D2StringList.Strings[4]);
  D2[5] := StrToInt(D2StringList.Strings[5]);
  D2[6] := StrToInt(D2StringList.Strings[6]);
  D2[7] := StrToInt(D2StringList.Strings[7]);
  D2[8] := StrToInt(D2StringList.Strings[8]);
  D2[9] := StrToInt(D2StringList.Strings[9]);
  D2[10] := StrToInt(D2StringList.Strings[10]);
  D2[11] := StrToInt(D2StringList.Strings[11]);
  D2[12] := StrToInt(D2StringList.Strings[12]);

  setlength(D3, CMPZSUM);
  D3[0] := StrToInt(D3StringList.Strings[0]);
  D3[1] := StrToInt(D3StringList.Strings[1]);
  D3[2] := StrToInt(D3StringList.Strings[2]);
  D3[3] := StrToInt(D3StringList.Strings[3]);
  D3[4] := StrToInt(D3StringList.Strings[4]);
  D3[5] := StrToInt(D3StringList.Strings[5]);
  D3[6] := StrToInt(D3StringList.Strings[6]);
  D3[7] := StrToInt(D3StringList.Strings[7]);
  D3[8] := StrToInt(D3StringList.Strings[8]);
  D3[9] := StrToInt(D3StringList.Strings[9]);
  D3[10] := StrToInt(D3StringList.Strings[10]);
  D3[11] := StrToInt(D3StringList.Strings[11]);
  D3[12] := StrToInt(D3StringList.Strings[12]);

  setlength(D4, CMPZSUM);
  D4[0] := StrToInt(D4StringList.Strings[0]);
  D4[1] := StrToInt(D4StringList.Strings[1]);
  D4[2] := StrToInt(D4StringList.Strings[2]);
  D4[3] := StrToInt(D4StringList.Strings[3]);
  D4[4] := StrToInt(D4StringList.Strings[4]);
  D4[5] := StrToInt(D4StringList.Strings[5]);
  D4[6] := StrToInt(D4StringList.Strings[6]);
  D4[7] := StrToInt(D4StringList.Strings[7]);
  D4[8] := StrToInt(D4StringList.Strings[8]);
  D4[9] := StrToInt(D4StringList.Strings[9]);
  D4[10] := StrToInt(D4StringList.Strings[10]);
  D4[11] := StrToInt(D4StringList.Strings[11]);
  D4[12] := StrToInt(D4StringList.Strings[12]);

  setlength(D5, CMPZSUM);
  D5[0] := StrToInt(D5StringList.Strings[0]);
  D5[1] := StrToInt(D5StringList.Strings[1]);
  D5[2] := StrToInt(D5StringList.Strings[2]);
  D5[3] := StrToInt(D5StringList.Strings[3]);
  D5[4] := StrToInt(D5StringList.Strings[4]);
  D5[5] := StrToInt(D5StringList.Strings[5]);
  D5[6] := StrToInt(D5StringList.Strings[6]);
  D5[7] := StrToInt(D5StringList.Strings[7]);
  D5[8] := StrToInt(D5StringList.Strings[8]);
  D5[9] := StrToInt(D5StringList.Strings[9]);
  D5[10] := StrToInt(D5StringList.Strings[10]);
  D5[11] := StrToInt(D5StringList.Strings[11]);
  D5[12] := StrToInt(D5StringList.Strings[12]);

  setlength(D6, CMPZSUM);
  D6[0] := StrToInt(D6StringList.Strings[0]);
  D6[1] := StrToInt(D6StringList.Strings[1]);
  D6[2] := StrToInt(D6StringList.Strings[2]);
  D6[3] := StrToInt(D6StringList.Strings[3]);
  D6[4] := StrToInt(D6StringList.Strings[4]);
  D6[5] := StrToInt(D6StringList.Strings[5]);
  D6[6] := StrToInt(D6StringList.Strings[6]);
  D6[7] := StrToInt(D6StringList.Strings[7]);
  D6[8] := StrToInt(D6StringList.Strings[8]);
  D6[9] := StrToInt(D6StringList.Strings[9]);
  D6[10] := StrToInt(D6StringList.Strings[10]);
  D6[11] := StrToInt(D6StringList.Strings[11]);
  D6[12] := StrToInt(D6StringList.Strings[12]);

  setlength(D7, CMPZSUM);
  D7[0] := StrToInt(D7StringList.Strings[0]);
  D7[1] := StrToInt(D7StringList.Strings[1]);
  D7[2] := StrToInt(D7StringList.Strings[2]);
  D7[3] := StrToInt(D7StringList.Strings[3]);
  D7[4] := StrToInt(D7StringList.Strings[4]);
  D7[5] := StrToInt(D7StringList.Strings[5]);
  D7[6] := StrToInt(D7StringList.Strings[6]);
  D7[7] := StrToInt(D7StringList.Strings[7]);
  D7[8] := StrToInt(D7StringList.Strings[8]);
  D7[9] := StrToInt(D7StringList.Strings[9]);
  D7[10] := StrToInt(D7StringList.Strings[10]);
  D7[11] := StrToInt(D7StringList.Strings[11]);
  D7[12] := StrToInt(D7StringList.Strings[12]);

  setlength(D8, CMPZSUM);
  D8[0] := StrToInt(D8StringList.Strings[0]);
  D8[1] := StrToInt(D8StringList.Strings[1]);
  D8[2] := StrToInt(D8StringList.Strings[2]);
  D8[3] := StrToInt(D8StringList.Strings[3]);
  D8[4] := StrToInt(D8StringList.Strings[4]);
  D8[5] := StrToInt(D8StringList.Strings[5]);
  D8[6] := StrToInt(D8StringList.Strings[6]);
  D8[7] := StrToInt(D8StringList.Strings[7]);
  D8[8] := StrToInt(D8StringList.Strings[8]);
  D8[9] := StrToInt(D8StringList.Strings[9]);
  D8[10] := StrToInt(D8StringList.Strings[10]);
  D8[11] := StrToInt(D8StringList.Strings[11]);
  D8[12] := StrToInt(D8StringList.Strings[12]);

  setlength(D9, CMPZSUM);
  D9[0] := StrToInt(D9StringList.Strings[0]);
  D9[1] := StrToInt(D9StringList.Strings[1]);
  D9[2] := StrToInt(D9StringList.Strings[2]);
  D9[3] := StrToInt(D9StringList.Strings[3]);
  D9[4] := StrToInt(D9StringList.Strings[4]);
  D9[5] := StrToInt(D9StringList.Strings[5]);
  D9[6] := StrToInt(D9StringList.Strings[6]);
  D9[7] := StrToInt(D9StringList.Strings[7]);
  D9[8] := StrToInt(D9StringList.Strings[8]);
  D9[9] := StrToInt(D9StringList.Strings[9]);
  D9[10] := StrToInt(D9StringList.Strings[10]);
  D9[11] := StrToInt(D9StringList.Strings[11]);
  D9[12] := StrToInt(D9StringList.Strings[12]);

  setlength(D10, CMPZSUM);
  D10[0] := StrToInt(D10StringList.Strings[0]);
  D10[1] := StrToInt(D10StringList.Strings[1]);
  D10[2] := StrToInt(D10StringList.Strings[2]);
  D10[3] := StrToInt(D10StringList.Strings[3]);
  D10[4] := StrToInt(D10StringList.Strings[4]);
  D10[5] := StrToInt(D10StringList.Strings[5]);
  D10[6] := StrToInt(D10StringList.Strings[6]);
  D10[7] := StrToInt(D10StringList.Strings[7]);
  D10[8] := StrToInt(D10StringList.Strings[8]);
  D10[9] := StrToInt(D10StringList.Strings[9]);
  D10[10] := StrToInt(D10StringList.Strings[10]);
  D10[11] := StrToInt(D10StringList.Strings[11]);
  D10[12] := StrToInt(D10StringList.Strings[12]);

  // D1处理
  for k := 0 to CMPZSUM - 1 do
  begin
    if D1[k] > 0 then
    begin
      if SS1[k] - D1[k] >= 0 then
      begin
        SD1[k] := D1[k];
        D1[k] := 0;
        SS1[k] := SS1[k] - SD1[k];
        S1StringList_D1.Add(IntToStr(SD1[k]));
      end
      else
      begin
        SD1[k] := SS1[k];
        D1[k] := D1[k] - SD1[k];
        SS1[k] := SS1[k] - SD1[k];
        S1StringList_D1.Add(IntToStr(SD1[k]));
      end;
    end
    else
    begin
      S1StringList_D1.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D1[k] > 0 then
    begin
      if SS2[k] - D1[k] >= 0 then
      begin
        SD1[k] := D1[k];
        D1[k] := 0;
        SS2[k] := SS2[k] - SD1[k];
        S2StringList_D1.Add(IntToStr(SD1[k]));
      end
      else
      begin
        SD1[k] := SS2[k];
        D1[k] := D1[k] - SD1[k];
        SS2[k] := SS2[k] - SD1[k];
        S2StringList_D1.Add(IntToStr(SD1[k]));
      end;
    end
    else
    begin
      S2StringList_D1.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D1[k] > 0 then
    begin
      if SS3[k] - D1[k] >= 0 then
      begin
        SD1[k] := D1[k];
        D1[k] := 0;
        SS3[k] := SS3[k] - SD1[k];
        S3StringList_D1.Add(IntToStr(SD1[k]));
      end
      else
      begin
        SD1[k] := SS3[k];
        D1[k] := D1[k] - SD1[k];
        SS3[k] := SS3[k] - SD1[k];
        S3StringList_D1.Add(IntToStr(SD1[k]));
      end;
    end
    else
    begin
      S3StringList_D1.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D1[k] > 0 then
    begin
      if SS4[k] - D1[k] >= 0 then
      begin
        SD1[k] := D1[k];
        D1[k] := 0;
        SS4[k] := SS4[k] - SD1[k];
        S4StringList_D1.Add(IntToStr(SD1[k]));
      end
      else
      begin
        SD1[k] := SS4[k];
        D1[k] := D1[k] - SD1[k];
        SS4[k] := SS4[k] - SD1[k];
        S4StringList_D1.Add(IntToStr(SD1[k]));
      end;
    end
    else
    begin
      S4StringList_D1.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D1[k] > 0 then
    begin
      if SS5[k] - D1[k] >= 0 then
      begin
        SD1[k] := D1[k];
        D1[k] := 0;
        SS5[k] := SS5[k] - SD1[k];
        S5StringList_D1.Add(IntToStr(SD1[k]));
      end
      else
      begin
        SD1[k] := SS5[k];
        D1[k] := D1[k] - SD1[k];
        SS5[k] := SS5[k] - SD1[k];
        S5StringList_D1.Add(IntToStr(SD1[k]));
      end;
    end
    else
    begin
      S5StringList_D1.Add('0');
    end;
  end;

  // D2处理
  for k := 0 to CMPZSUM - 1 do
  begin
    if D2[k] > 0 then
    begin
      if SS1[k] - D2[k] >= 0 then
      begin
        SD2[k] := D2[k];
        D2[k] := 0;
        SS1[k] := SS1[k] - SD2[k];
        S1StringList_D2.Add(IntToStr(SD2[k]));
      end
      else
      begin
        SD2[k] := SS1[k];
        D2[k] := D2[k] - SD2[k];
        SS1[k] := SS1[k] - SD2[k];
        S1StringList_D2.Add(IntToStr(SD2[k]));
      end;
    end
    else
    begin
      S1StringList_D2.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D2[k] > 0 then
    begin
      if SS2[k] - D2[k] >= 0 then
      begin
        SD2[k] := D2[k];
        D2[k] := 0;
        SS2[k] := SS2[k] - SD2[k];
        S2StringList_D2.Add(IntToStr(SD2[k]));
      end
      else
      begin
        SD2[k] := SS2[k];
        D2[k] := D2[k] - SD2[k];
        SS2[k] := SS2[k] - SD2[k];
        S2StringList_D2.Add(IntToStr(SD2[k]));
      end;
    end
    else
    begin
      S2StringList_D2.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D2[k] > 0 then
    begin
      if SS3[k] - D2[k] >= 0 then
      begin
        SD2[k] := D2[k];
        D2[k] := 0;
        SS3[k] := SS3[k] - SD2[k];
        S3StringList_D2.Add(IntToStr(SD2[k]));
      end
      else
      begin
        SD2[k] := SS3[k];
        D2[k] := D2[k] - SD2[k];
        SS3[k] := SS3[k] - SD2[k];
        S3StringList_D2.Add(IntToStr(SD2[k]));
      end;
    end
    else
    begin
      S3StringList_D2.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D2[k] > 0 then
    begin
      if SS4[k] - D2[k] >= 0 then
      begin
        SD2[k] := D2[k];
        D2[k] := 0;
        SS4[k] := SS4[k] - SD2[k];
        S4StringList_D2.Add(IntToStr(SD2[k]));
      end
      else
      begin
        SD2[k] := SS4[k];
        D2[k] := D2[k] - SD2[k];
        SS4[k] := SS4[k] - SD2[k];
        S4StringList_D2.Add(IntToStr(SD2[k]));
      end;
    end
    else
    begin
      S4StringList_D2.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D2[k] > 0 then
    begin
      if SS5[k] - D2[k] >= 0 then
      begin
        SD2[k] := D2[k];
        D2[k] := 0;
        SS5[k] := SS5[k] - SD2[k];
        S5StringList_D2.Add(IntToStr(SD2[k]));
      end
      else
      begin
        SD2[k] := SS5[k];
        D2[k] := D2[k] - SD2[k];
        SS5[k] := SS5[k] - SD2[k];
        S5StringList_D2.Add(IntToStr(SD2[k]));
      end;
    end
    else
    begin
      S5StringList_D2.Add('0');
    end;
  end;

  // D3处理
  for k := 0 to CMPZSUM - 1 do
  begin
    if D3[k] > 0 then
    begin
      if SS1[k] - D3[k] >= 0 then
      begin
        SD3[k] := D3[k];
        D3[k] := 0;
        SS1[k] := SS1[k] - SD3[k];
        S1StringList_D3.Add(IntToStr(SD3[k]));
      end
      else
      begin
        SD3[k] := SS1[k];
        D3[k] := D3[k] - SD3[k];
        SS1[k] := SS1[k] - SD3[k];
        S1StringList_D3.Add(IntToStr(SD3[k]));
      end;
    end
    else
    begin
      S1StringList_D3.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D3[k] > 0 then
    begin
      if SS2[k] - D3[k] >= 0 then
      begin
        SD3[k] := D3[k];
        D3[k] := 0;
        SS2[k] := SS2[k] - SD3[k];
        S2StringList_D3.Add(IntToStr(SD3[k]));
      end
      else
      begin
        SD3[k] := SS2[k];
        D3[k] := D3[k] - SD3[k];
        SS2[k] := SS2[k] - SD3[k];
        S2StringList_D3.Add(IntToStr(SD3[k]));
      end;
    end
    else
    begin
      S2StringList_D3.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D3[k] > 0 then
    begin
      if SS3[k] - D3[k] >= 0 then
      begin
        SD3[k] := D3[k];
        D3[k] := 0;
        SS3[k] := SS3[k] - SD3[k];
        S3StringList_D3.Add(IntToStr(SD3[k]));
      end
      else
      begin
        SD3[k] := SS3[k];
        D3[k] := D3[k] - SD3[k];
        SS3[k] := SS3[k] - SD3[k];
        S3StringList_D3.Add(IntToStr(SD3[k]));
      end;
    end
    else
    begin
      S3StringList_D3.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D3[k] > 0 then
    begin
      if SS4[k] - D3[k] >= 0 then
      begin
        SD3[k] := D3[k];
        D3[k] := 0;
        SS4[k] := SS4[k] - SD3[k];
        S4StringList_D3.Add(IntToStr(SD3[k]));
      end
      else
      begin
        SD3[k] := SS4[k];
        D3[k] := D3[k] - SD3[k];
        SS4[k] := SS4[k] - SD3[k];
        S4StringList_D3.Add(IntToStr(SD3[k]));
      end;
    end
    else
    begin
      S4StringList_D3.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D3[k] > 0 then
    begin
      if SS5[k] - D3[k] >= 0 then
      begin
        SD3[k] := D3[k];
        D3[k] := 0;
        SS5[k] := SS5[k] - SD3[k];
        S5StringList_D3.Add(IntToStr(SD3[k]));
      end
      else
      begin
        SD3[k] := SS5[k];
        D3[k] := D3[k] - SD3[k];
        SS5[k] := SS5[k] - SD3[k];
        S5StringList_D3.Add(IntToStr(SD3[k]));
      end;
    end
    else
    begin
      S5StringList_D3.Add('0');
    end;
  end;

  // D4处理
  for k := 0 to CMPZSUM - 1 do
  begin
    if D4[k] > 0 then
    begin
      if SS1[k] - D4[k] >= 0 then
      begin
        SD4[k] := D4[k];
        D4[k] := 0;
        SS1[k] := SS1[k] - SD4[k];
        S1StringList_D4.Add(IntToStr(SD4[k]));
      end
      else
      begin
        SD4[k] := SS1[k];
        D4[k] := D4[k] - SD4[k];
        SS1[k] := SS1[k] - SD4[k];
        S1StringList_D4.Add(IntToStr(SD4[k]));
      end;
    end
    else
    begin
      S1StringList_D4.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D4[k] > 0 then
    begin
      if SS2[k] - D4[k] >= 0 then
      begin
        SD4[k] := D4[k];
        D4[k] := 0;
        SS2[k] := SS2[k] - SD4[k];
        S2StringList_D4.Add(IntToStr(SD4[k]));
      end
      else
      begin
        SD4[k] := SS2[k];
        D4[k] := D4[k] - SD4[k];
        SS2[k] := SS2[k] - SD4[k];
        S2StringList_D4.Add(IntToStr(SD4[k]));
      end;
    end
    else
    begin
      S2StringList_D4.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D4[k] > 0 then
    begin
      if SS3[k] - D4[k] >= 0 then
      begin
        SD4[k] := D4[k];
        D4[k] := 0;
        SS3[k] := SS3[k] - SD4[k];
        S3StringList_D4.Add(IntToStr(SD4[k]));
      end
      else
      begin
        SD4[k] := SS4[k];
        D4[k] := D4[k] - SD4[k];
        SS3[k] := SS3[k] - SD4[k];
        S3StringList_D4.Add(IntToStr(SD4[k]));
      end;
    end
    else
    begin
      S3StringList_D4.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D4[k] > 0 then
    begin
      if SS4[k] - D4[k] >= 0 then
      begin
        SD4[k] := D4[k];
        D4[k] := 0;
        SS4[k] := SS4[k] - SD4[k];
        S4StringList_D4.Add(IntToStr(SD4[k]));
      end
      else
      begin
        SD4[k] := SS4[k];
        D4[k] := D4[k] - SD4[k];
        SS4[k] := SS4[k] - SD4[k];
        S4StringList_D4.Add(IntToStr(SD4[k]));
      end;
    end
    else
    begin
      S4StringList_D4.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D5[k] > 0 then
    begin
      if SS5[k] - D4[k] >= 0 then
      begin
        SD4[k] := D4[k];
        D4[k] := 0;
        SS5[k] := SS5[k] - SD4[k];
        S5StringList_D4.Add(IntToStr(SD4[k]));
      end
      else
      begin
        SD5[k] := SS5[k];
        D5[k] := D4[k] - SD4[k];
        SS5[k] := SS5[k] - SD4[k];
        S5StringList_D4.Add(IntToStr(SD4[k]));
      end;
    end
    else
    begin
      S5StringList_D4.Add('0');
    end;
  end;

  // D5处理
  for k := 0 to CMPZSUM - 1 do
  begin
    if D5[k] > 0 then
    begin
      if SS1[k] - D5[k] >= 0 then
      begin
        SD5[k] := D5[k];
        D5[k] := 0;
        SS1[k] := SS1[k] - SD5[k];
        S1StringList_D5.Add(IntToStr(SD5[k]));
      end
      else
      begin
        SD5[k] := SS1[k];
        D5[k] := D5[k] - SD5[k];
        SS1[k] := SS1[k] - SD5[k];
        S1StringList_D5.Add(IntToStr(SD5[k]));
      end;
    end
    else
    begin
      S1StringList_D5.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D5[k] > 0 then
    begin
      if SS2[k] - D5[k] >= 0 then
      begin
        SD5[k] := D5[k];
        D5[k] := 0;
        SS2[k] := SS2[k] - SD5[k];
        S2StringList_D5.Add(IntToStr(SD5[k]));
      end
      else
      begin
        SD5[k] := SS2[k];
        D5[k] := D5[k] - SD5[k];
        SS2[k] := SS2[k] - SD5[k];
        S2StringList_D5.Add(IntToStr(SD5[k]));
      end;
    end
    else
    begin
      S2StringList_D5.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D5[k] > 0 then
    begin
      if SS3[k] - D5[k] >= 0 then
      begin
        SD5[k] := D5[k];
        D5[k] := 0;
        SS3[k] := SS3[k] - SD5[k];
        S3StringList_D5.Add(IntToStr(SD5[k]));
      end
      else
      begin
        SD5[k] := SS3[k];
        D5[k] := D5[k] - SD5[k];
        SS3[k] := SS3[k] - SD5[k];
        S3StringList_D5.Add(IntToStr(SD5[k]));
      end;
    end
    else
    begin
      S3StringList_D5.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D5[k] > 0 then
    begin
      if SS4[k] - D5[k] >= 0 then
      begin
        SD5[k] := D5[k];
        D5[k] := 0;
        SS4[k] := SS4[k] - SD5[k];
        S4StringList_D5.Add(IntToStr(SD5[k]));
      end
      else
      begin
        SD5[k] := SS4[k];
        D5[k] := D5[k] - SD5[k];
        SS4[k] := SS4[k] - SD5[k];
        S4StringList_D5.Add(IntToStr(SD5[k]));
      end;
    end
    else
    begin
      S4StringList_D5.Add('0');
    end;
  end;
  for k := 0 to CMPZSUM - 1 do
  begin
    if D5[k] > 0 then
    begin
      if SS5[k] - D5[k] >= 0 then
      begin
        SD5[k] := D5[k];
        D5[k] := 0;
        SS5[k] := SS5[k] - SD5[k];
        S5StringList_D5.Add(IntToStr(SD5[k]));
      end
      else
      begin
        SD5[k] := SS5[k];
        D5[k] := D5[k] - SD5[k];
        SS5[k] := SS5[k] - SD5[k];
        S5StringList_D5.Add(IntToStr(SD5[k]));
      end;
    end
    else
    begin
      S5StringList_D5.Add('0');
    end;
  end;

  // D6处理
  for k := 0 to CMPZSUM - 1 do
  begin
    if D6[k] > 0 then
    begin
      if SS1[k] - D6[k] >= 0 then
      begin
        SD6[k] := D6[k];
        D6[k] := 0;
        SS1[k] := SS1[k] - SD6[k];
        S1StringList_D6.Add(IntToStr(SD6[k]));
      end
      else
      begin
        SD6[k] := SS1[k];
        D6[k] := D6[k] - SD6[k];
        SS1[k] := SS1[k] - SD6[k];
        S1StringList_D6.Add(IntToStr(SD6[k]));
      end;
    end
    else
    begin
      S1StringList_D6.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D6[k] > 0 then
    begin
      if SS2[k] - D6[k] >= 0 then
      begin
        SD6[k] := D6[k];
        D6[k] := 0;
        SS2[k] := SS2[k] - SD6[k];
        S2StringList_D6.Add(IntToStr(SD6[k]));
      end
      else
      begin
        SD6[k] := SS2[k];
        D6[k] := D6[k] - SD6[k];
        SS2[k] := SS2[k] - SD6[k];
        S2StringList_D6.Add(IntToStr(SD6[k]));
      end;
    end
    else
    begin
      S2StringList_D6.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D6[k] > 0 then
    begin
      if SS3[k] - D6[k] >= 0 then
      begin
        SD6[k] := D6[k];
        D6[k] := 0;
        SS3[k] := SS3[k] - SD6[k];
        S3StringList_D6.Add(IntToStr(SD6[k]));
      end
      else
      begin
        SD6[k] := SS3[k];
        D6[k] := D6[k] - SD6[k];
        SS3[k] := SS3[k] - SD6[k];
        S3StringList_D6.Add(IntToStr(SD6[k]));
      end;
    end
    else
    begin
      S3StringList_D6.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D6[k] > 0 then
    begin
      if SS4[k] - D6[k] >= 0 then
      begin
        SD6[k] := D6[k];
        D6[k] := 0;
        SS4[k] := SS4[k] - SD6[k];
        S4StringList_D6.Add(IntToStr(SD6[k]));
      end
      else
      begin
        SD6[k] := SS4[k];
        D6[k] := D6[k] - SD6[k];
        SS4[k] := SS4[k] - SD6[k];
        S4StringList_D6.Add(IntToStr(SD6[k]));
      end;
    end
    else
    begin
      S4StringList_D6.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D6[k] > 0 then
    begin
      if SS5[k] - D6[k] >= 0 then
      begin
        SD6[k] := D6[k];
        D6[k] := 0;
        SS5[k] := SS5[k] - SD6[k];
        S5StringList_D6.Add(IntToStr(SD6[k]));
      end
      else
      begin
        SD6[k] := SS5[k];
        D6[k] := D6[k] - SD6[k];
        SS5[k] := SS5[k] - SD6[k];
        S5StringList_D6.Add(IntToStr(SD6[k]));
      end;
    end
    else
    begin
      S5StringList_D6.Add('0');
    end;
  end;

  // D7处理
  for k := 0 to CMPZSUM - 1 do
  begin
    if D7[k] > 0 then
    begin
      if SS1[k] - D7[k] >= 0 then
      begin
        SD7[k] := D7[k];
        D7[k] := 0;
        SS1[k] := SS1[k] - SD7[k];
        S1StringList_D7.Add(IntToStr(SD7[k]));
      end
      else
      begin
        SD7[k] := SS1[k];
        D7[k] := D7[k] - SD7[k];
        SS1[k] := SS1[k] - SD7[k];
        S1StringList_D7.Add(IntToStr(SD7[k]));
      end;
    end
    else
    begin
      S1StringList_D7.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D7[k] > 0 then
    begin
      if SS2[k] - D7[k] >= 0 then
      begin
        SD7[k] := D7[k];
        D7[k] := 0;
        SS2[k] := SS2[k] - SD7[k];
        S2StringList_D7.Add(IntToStr(SD7[k]));
      end
      else
      begin
        SD7[k] := SS2[k];
        D7[k] := D7[k] - SD7[k];
        SS2[k] := SS2[k] - SD7[k];
        S2StringList_D7.Add(IntToStr(SD7[k]));
      end;
    end
    else
    begin
      S2StringList_D7.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D7[k] > 0 then
    begin
      if SS3[k] - D7[k] >= 0 then
      begin
        SD7[k] := D7[k];
        D7[k] := 0;
        SS3[k] := SS3[k] - SD7[k];
        S3StringList_D7.Add(IntToStr(SD7[k]));
      end
      else
      begin
        SD7[k] := SS3[k];
        D7[k] := D7[k] - SD7[k];
        SS3[k] := SS3[k] - SD7[k];
        S3StringList_D7.Add(IntToStr(SD7[k]));
      end;
    end
    else
    begin
      S3StringList_D7.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D7[k] > 0 then
    begin
      if SS4[k] - D7[k] >= 0 then
      begin
        SD7[k] := D7[k];
        D7[k] := 0;
        SS4[k] := SS4[k] - SD7[k];
        S4StringList_D7.Add(IntToStr(SD7[k]));
      end
      else
      begin
        SD7[k] := SS4[k];
        D7[k] := D7[k] - SD7[k];
        SS4[k] := SS4[k] - SD7[k];
        S4StringList_D7.Add(IntToStr(SD7[k]));
      end;
    end
    else
    begin
      S4StringList_D7.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D7[k] > 0 then
    begin
      if SS5[k] - D7[k] >= 0 then
      begin
        SD7[k] := D7[k];
        D7[k] := 0;
        SS5[k] := SS5[k] - SD7[k];
        S5StringList_D7.Add(IntToStr(SD7[k]));
      end
      else
      begin
        SD7[k] := SS5[k];
        D7[k] := D7[k] - SD7[k];
        SS5[k] := SS5[k] - SD7[k];
        S5StringList_D7.Add(IntToStr(SD7[k]));
      end;
    end
    else
    begin
      S5StringList_D7.Add('0');
    end;
  end;

  // D8处理
  for k := 0 to CMPZSUM - 1 do
  begin
    if D8[k] > 0 then
    begin
      if SS1[k] - D8[k] >= 0 then
      begin
        SD8[k] := D8[k];
        D8[k] := 0;
        SS1[k] := SS1[k] - SD8[k];
        S1StringList_D8.Add(IntToStr(SD8[k]));
      end
      else
      begin
        SD8[k] := SS1[k];
        D8[k] := D8[k] - SD8[k];
        SS1[k] := SS1[k] - SD8[k];
        S1StringList_D8.Add(IntToStr(SD8[k]));
      end;
    end
    else
    begin
      S1StringList_D8.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D8[k] > 0 then
    begin
      if SS2[k] - D8[k] >= 0 then
      begin
        SD8[k] := D8[k];
        D8[k] := 0;
        SS2[k] := SS2[k] - SD8[k];
        S2StringList_D8.Add(IntToStr(SD8[k]));
      end
      else
      begin
        SD8[k] := SS2[k];
        D8[k] := D8[k] - SD8[k];
        SS2[k] := SS2[k] - SD8[k];
        S2StringList_D8.Add(IntToStr(SD8[k]));
      end;
    end
    else
    begin
      S2StringList_D8.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D8[k] > 0 then
    begin
      if SS3[k] - D8[k] >= 0 then
      begin
        SD8[k] := D8[k];
        D8[k] := 0;
        SS3[k] := SS3[k] - SD8[k];
        S3StringList_D8.Add(IntToStr(SD8[k]));
      end
      else
      begin
        SD8[k] := SS3[k];
        D8[k] := D8[k] - SD8[k];
        SS3[k] := SS3[k] - SD8[k];
        S3StringList_D8.Add(IntToStr(SD8[k]));
      end;
    end
    else
    begin
      S3StringList_D8.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D8[k] > 0 then
    begin
      if SS4[k] - D8[k] >= 0 then
      begin
        SD8[k] := D8[k];
        D8[k] := 0;
        SS4[k] := SS4[k] - SD8[k];
        S4StringList_D8.Add(IntToStr(SD8[k]));
      end
      else
      begin
        SD8[k] := SS4[k];
        D8[k] := D8[k] - SD8[k];
        SS4[k] := SS4[k] - SD8[k];
        S4StringList_D8.Add(IntToStr(SD8[k]));
      end;
    end
    else
    begin
      S4StringList_D8.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D8[k] > 0 then
    begin
      if SS5[k] - D8[k] >= 0 then
      begin
        SD8[k] := D8[k];
        D8[k] := 0;
        SS5[k] := SS5[k] - SD8[k];
        S5StringList_D8.Add(IntToStr(SD8[k]));
      end
      else
      begin
        SD8[k] := SS5[k];
        D8[k] := D8[k] - SD8[k];
        SS5[k] := SS5[k] - SD8[k];
        S5StringList_D8.Add(IntToStr(SD8[k]));
      end;
    end
    else
    begin
      S5StringList_D8.Add('0');
    end;
  end;

  // D9处理
  for k := 0 to CMPZSUM - 1 do
  begin
    if D9[k] > 0 then
    begin
      if SS1[k] - D9[k] >= 0 then
      begin
        SD9[k] := D9[k];
        D9[k] := 0;
        SS1[k] := SS1[k] - SD9[k];
        S1StringList_D9.Add(IntToStr(SD9[k]));
      end
      else
      begin
        SD9[k] := SS1[k];
        D9[k] := D9[k] - SD9[k];
        SS1[k] := SS1[k] - SD9[k];
        S1StringList_D9.Add(IntToStr(SD9[k]));
      end;
    end
    else
    begin
      S1StringList_D9.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D9[k] > 0 then
    begin
      if SS2[k] - D9[k] >= 0 then
      begin
        SD9[k] := D9[k];
        D9[k] := 0;
        SS2[k] := SS2[k] - SD9[k];
        S2StringList_D9.Add(IntToStr(SD9[k]));
      end
      else
      begin
        SD9[k] := SS2[k];
        D9[k] := D9[k] - SD9[k];
        SS2[k] := SS2[k] - SD9[k];
        S2StringList_D9.Add(IntToStr(SD9[k]));
      end;
    end
    else
    begin
      S2StringList_D9.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D9[k] > 0 then
    begin
      if SS3[k] - D9[k] >= 0 then
      begin
        SD9[k] := D9[k];
        D9[k] := 0;
        SS3[k] := SS3[k] - SD9[k];
        S3StringList_D9.Add(IntToStr(SD9[k]));
      end
      else
      begin
        SD9[k] := SS3[k];
        D9[k] := D9[k] - SD9[k];
        SS3[k] := SS3[k] - SD9[k];
        S3StringList_D9.Add(IntToStr(SD9[k]));
      end;
    end
    else
    begin
      S3StringList_D9.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D9[k] > 0 then
    begin
      if SS4[k] - D9[k] >= 0 then
      begin
        SD9[k] := D9[k];
        D9[k] := 0;
        SS4[k] := SS4[k] - SD9[k];
        S4StringList_D9.Add(IntToStr(SD9[k]));
      end
      else
      begin
        SD9[k] := SS4[k];
        D9[k] := D9[k] - SD9[k];
        SS4[k] := SS4[k] - SD9[k];
        S4StringList_D9.Add(IntToStr(SD9[k]));
      end;
    end
    else
    begin
      S4StringList_D9.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D9[k] > 0 then
    begin
      if SS5[k] - D9[k] >= 0 then
      begin
        SD9[k] := D9[k];
        D9[k] := 0;
        SS5[k] := SS5[k] - SD9[k];
        S5StringList_D9.Add(IntToStr(SD9[k]));
      end
      else
      begin
        SD9[k] := SS5[k];
        D9[k] := D9[k] - SD9[k];
        SS5[k] := SS5[k] - SD9[k];
        S5StringList_D9.Add(IntToStr(SD9[k]));
      end;
    end
    else
    begin
      S5StringList_D9.Add('0');
    end;
  end;

  // D10处理
  for k := 0 to CMPZSUM - 1 do
  begin
    if D10[k] > 0 then
    begin
      if SS1[k] - D10[k] >= 0 then
      begin
        SD10[k] := D10[k];
        D10[k] := 0;
        SS1[k] := SS1[k] - SD10[k];
        S1StringList_D10.Add(IntToStr(SD10[k]));
      end
      else
      begin
        SD10[k] := SS1[k];
        D10[k] := D10[k] - SD10[k];
        SS1[k] := SS1[k] - SD10[k];
        S1StringList_D10.Add(IntToStr(SD10[k]));
      end;
    end
    else
    begin
      S1StringList_D10.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D10[k] > 0 then
    begin
      if SS2[k] - D10[k] >= 0 then
      begin
        SD10[k] := D10[k];
        D10[k] := 0;
        SS2[k] := SS2[k] - SD10[k];
        S2StringList_D10.Add(IntToStr(SD10[k]));
      end
      else
      begin
        SD10[k] := SS2[k];
        D10[k] := D10[k] - SD10[k];
        SS2[k] := SS2[k] - SD10[k];
        S2StringList_D10.Add(IntToStr(SD10[k]));
      end;
    end
    else
    begin
      S2StringList_D10.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D10[k] > 0 then
    begin
      if SS3[k] - D10[k] >= 0 then
      begin
        SD10[k] := D10[k];
        D10[k] := 0;
        SS3[k] := SS3[k] - SD10[k];
        S3StringList_D10.Add(IntToStr(SD10[k]));
      end
      else
      begin
        SD10[k] := SS3[k];
        D10[k] := D10[k] - SD10[k];
        SS3[k] := SS3[k] - SD10[k];
        S3StringList_D10.Add(IntToStr(SD10[k]));
      end;
    end
    else
    begin
      S3StringList_D10.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D10[k] > 0 then
    begin
      if SS4[k] - D10[k] >= 0 then
      begin
        SD10[k] := D10[k];
        D10[k] := 0;
        SS4[k] := SS4[k] - SD10[k];
        S4StringList_D10.Add(IntToStr(SD10[k]));
      end
      else
      begin
        SD10[k] := SS4[k];
        D10[k] := D10[k] - SD10[k];
        SS4[k] := SS4[k] - SD10[k];
        S4StringList_D10.Add(IntToStr(SD10[k]));
      end;
    end
    else
    begin
      S4StringList_D10.Add('0');
    end;
  end;

  for k := 0 to CMPZSUM - 1 do
  begin
    if D10[k] > 0 then
    begin
      if SS5[k] - D10[k] >= 0 then
      begin
        SD10[k] := D10[k];
        D10[k] := 0;
        SS5[k] := SS5[k] - SD10[k];
        S5StringList_D10.Add(IntToStr(SD10[k]));
      end
      else
      begin
        SD10[k] := SS5[k];
        D10[k] := D10[k] - SD10[k];
        SS5[k] := SS5[k] - SD10[k];
        S5StringList_D10.Add(IntToStr(SD10[k]));
      end;
    end
    else
    begin
      S5StringList_D10.Add('0');
    end;
  end;

  // // SS1, SS2, SS3, SS4, SS5: Array of Integer;
  // // KS1, KS2, KS3, KS4, KS5: Array of Integer;
  // // SSALL1, SSALL2, SSALL3, SSALL4, SSALL5: Array of Integer;
  //
  SSALL1StringList := TStringList.Create;
  Kc1StringList := TStringList.Create;

  MATERIALFZID1 := SSALL1[13];
  cmtypenum1 := SSALL1[14];
  if MATERIALFZID1 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL1StringList.Add(IntToStr(SSALL1[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc1StringList.Add(IntToStr(KS1[k]));
    end;

    FillGrid2TenDetail(SSALL1StringList, Kc1StringList, S1StringList_D1,
      S1StringList_D2, S1StringList_D3, S1StringList_D4, S1StringList_D5,
      S1StringList_D6, S1StringList_D7, S1StringList_D8, S1StringList_D9,
      S1StringList_D10, MATERIALFZID1, cmtypenum1);
  end;

  SSALL2StringList := TStringList.Create;
  Kc2StringList := TStringList.Create;

  MATERIALFZID2 := SSALL2[13];
  cmtypenum2 := SSALL2[14];
  if MATERIALFZID2 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL2StringList.Add(IntToStr(SSALL2[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc2StringList.Add(IntToStr(KS2[k]));
    end;

    FillGrid2TenDetail(SSALL2StringList, Kc2StringList, S2StringList_D1,
      S2StringList_D2, S2StringList_D3, S2StringList_D4, S2StringList_D5,
      S2StringList_D6, S2StringList_D7, S2StringList_D8, S2StringList_D9,
      S2StringList_D10, MATERIALFZID2, cmtypenum2);
  end;

  SSALL3StringList := TStringList.Create;
  Kc3StringList := TStringList.Create;

  MATERIALFZID3 := SSALL3[13];
  cmtypenum3 := SSALL3[14];
  if MATERIALFZID3 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL3StringList.Add(IntToStr(SSALL3[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc3StringList.Add(IntToStr(KS3[k]));
    end;

    FillGrid2TenDetail(SSALL3StringList, Kc3StringList, S3StringList_D1,
      S3StringList_D2, S3StringList_D3, S3StringList_D4, S3StringList_D5,
      S3StringList_D6, S3StringList_D7, S3StringList_D8, S3StringList_D9,
      S3StringList_D10, MATERIALFZID3, cmtypenum3);
  end;

  SSALL4StringList := TStringList.Create;
  Kc4StringList := TStringList.Create;

  MATERIALFZID4 := SSALL4[13];
  cmtypenum4 := SSALL4[14];
  if MATERIALFZID4 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL4StringList.Add(IntToStr(SSALL4[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc4StringList.Add(IntToStr(KS4[k]));
    end;

    FillGrid2TenDetail(SSALL4StringList, Kc4StringList, S4StringList_D1,
      S4StringList_D2, S4StringList_D3, S4StringList_D4, S4StringList_D5,
      S4StringList_D6, S4StringList_D7, S4StringList_D8, S4StringList_D9,
      S4StringList_D10, MATERIALFZID4, cmtypenum4);
  end;

  SSALL5StringList := TStringList.Create;
  Kc5StringList := TStringList.Create;

  MATERIALFZID5 := SSALL5[13];
  cmtypenum5 := SSALL5[14];
  if MATERIALFZID5 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL5StringList.Add(IntToStr(SSALL5[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc5StringList.Add(IntToStr(KS5[k]));
    end;

    FillGrid2TenDetail(SSALL5StringList, Kc5StringList, S5StringList_D1,
      S5StringList_D2, S5StringList_D3, S5StringList_D4, S5StringList_D5,
      S5StringList_D6, S5StringList_D7, S5StringList_D8, S5StringList_D9,
      S5StringList_D10, MATERIALFZID5, cmtypenum5);
  end;

end;

procedure TThreadPdCal.FillGrid2TenDetail(SAll, Kc, D1, D2, D3, D4, D5, D6, D7,
  D8, D9, D10: TStringList; MATERIALFZID, cmtypenum: Integer);
var
  o: Integer;
  Sum1, Sum2, Sum3, Sum4, Sum5, Sum6, Sum7, Sum8, Sum9, Sum10, Sum11: Integer;
  Sum_Kc, Sum_All: Integer;
  sequence: Integer;
  KcFlag: Boolean;
begin

  KcFlag := false;
  for o := 0 to CMPZSUM - 1 do
  begin
    if StrToInt(Kc[o]) > 0 then
    begin
      KcFlag := true;
      break;
    end;
  end;

  Sum1 := 0;
  Sum2 := 0;
  Sum3 := 0;
  Sum4 := 0;
  Sum5 := 0;
  Sum6 := 0;
  Sum7 := 0;
  Sum8 := 0;
  Sum9 := 0;
  Sum10 := 0;
  Sum11 := 0;

  Sum_Kc := 0;
  Sum_All := 0;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := '小计';
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(SAll[o]);
      Sum1 := Sum1 + StrToInt(SAll[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(SAll[o]);
      Sum1 := Sum1 + StrToInt(SAll[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(SAll[o]);
      Sum1 := Sum1 + StrToInt(SAll[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum1;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  // 如果有库存，加入库存数据
  if KcFlag then
  begin
    DMUtilSelect.UniQuery1.Close;
    DMUtilSelect.UniQuery1.Sql.Clear;
    DMUtilSelect.UniQuery1.Sql.Add
      ('select nextval (''cc_materialdetail'') seq');
    DMUtilSelect.UniQuery1.ExecSQL;

    if not DMUtilSelect.UniQuery1.Eof then
    begin
      sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
    end;
    DMUtilSelect.UniQuery1.Close;

    DMUtilSave.UniQuery1.Close;
    DMUtilSave.UniQuery1.Sql.Clear;
    DMUtilSave.UniQuery1.Sql.Add
      ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
      + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
    DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
    DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
    DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := '减库存';
    DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

    if cmtypenum = 1 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
          StrToInt(Kc[o]);
        Sum_Kc := Sum_Kc + StrToInt(Kc[o]);
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    if cmtypenum = 2 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
          StrToInt(Kc[o]);
        Sum_Kc := Sum_Kc + StrToInt(Kc[o]);
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    if cmtypenum = 3 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
          StrToInt(Kc[o]);
        Sum_Kc := Sum_Kc + StrToInt(Kc[o]);
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum_Kc;

    DMUtilSave.UniQuery1.ExecSQL;
    DMUtilSave.UniQuery1.Close;
  end;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 1 + G_PcAddNum;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D1[o]);
      Sum2 := Sum2 + StrToInt(D1[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D1[o]);
      Sum2 := Sum2 + StrToInt(D1[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D1[o]);
      Sum2 := Sum2 + StrToInt(D1[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum2;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 2 + G_PcAddNum;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D2[o]);
      Sum3 := Sum3 + StrToInt(D2[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D2[o]);
      Sum3 := Sum3 + StrToInt(D2[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D2[o]);
      Sum3 := Sum3 + StrToInt(D2[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum3;
  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 3 + G_PcAddNum;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D3[o]);
      Sum4 := Sum4 + StrToInt(D3[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D3[o]);
      Sum4 := Sum4 + StrToInt(D3[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D3[o]);
      Sum4 := Sum4 + StrToInt(D3[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum4;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 4 + G_PcAddNum;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D4[o]);
      Sum5 := Sum5 + StrToInt(D4[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D4[o]);
      Sum5 := Sum5 + StrToInt(D4[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D4[o]);
      Sum5 := Sum5 + StrToInt(D4[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum5;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 5 + G_PcAddNum;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D5[o]);
      Sum6 := Sum6 + StrToInt(D5[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D5[o]);
      Sum6 := Sum6 + StrToInt(D5[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D5[o]);
      Sum6 := Sum6 + StrToInt(D5[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum6;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 6 + G_PcAddNum;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D6[o]);
      Sum7 := Sum7 + StrToInt(D6[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D6[o]);
      Sum7 := Sum7 + StrToInt(D6[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D6[o]);
      Sum7 := Sum7 + StrToInt(D6[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum7;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 7 + G_PcAddNum;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D7[o]);
      Sum8 := Sum8 + StrToInt(D7[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D7[o]);
      Sum8 := Sum8 + StrToInt(D7[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D7[o]);
      Sum6 := Sum6 + StrToInt(D7[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum8;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 8 + G_PcAddNum;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D8[o]);
      Sum9 := Sum9 + StrToInt(D8[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D8[o]);
      Sum9 := Sum9 + StrToInt(D8[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D8[o]);
      Sum9 := Sum9 + StrToInt(D8[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum9;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 9 + G_PcAddNum;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D9[o]);
      Sum10 := Sum10 + StrToInt(D9[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D9[o]);
      Sum10 := Sum10 + StrToInt(D9[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D9[o]);
      Sum10 := Sum10 + StrToInt(D9[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum10;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 10 + G_PcAddNum;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D10[o]);
      Sum11 := Sum11 + StrToInt(D10[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D10[o]);
      Sum11 := Sum11 + StrToInt(D10[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(D10[o]);
      Sum11 := Sum11 + StrToInt(D10[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum11;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  // 插入空白行
  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 0;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value := 0;
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value := 0;
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value := 0;
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := 0;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('update cc_materialdetail set cjbc=:cjbc where MATERIALFZID=:MATERIALFZID');
  DMUtilSave.UniQuery1.ParamByName('cjbc').Value := G_bc;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('update cc_materialdetail set CJ_1=CM_1*(1+cjbc),CJ_2=CM_2*(1+cjbc),CJ_3=CM_3*(1+cjbc),CJ_4=CM_4*(1+cjbc),CJ_5=CM_5*(1+cjbc),CJ_6=CM_6*(1+cjbc),CJ_7=CM_7*(1+cjbc),CJ_8=CM_8*(1+cjbc),CJ_9=CM_9*(1+cjbc),CJ_10=CM_10*(1+cjbc),'
    + ' CJ_11=CM_11*(1+cjbc),CJ_12=CM_12*(1+cjbc),CJ_13=CM_13*(1+cjbc),CJ_14=CM_14*(1+cjbc),CJ_15=CM_15*(1+cjbc),CJ_Sum=Cm_Sum*(1+cjbc) where (CJ_Sum is null or CJ_Sum =0)');
  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;
end;

procedure TThreadPdCal.FillGridEmpty;
var
  S1StringList_D1, S1StringList_D2, S1StringList_D3: TStringList;
  S2StringList_D1, S2StringList_D2, S2StringList_D3: TStringList;
  S3StringList_D1, S3StringList_D2, S3StringList_D3: TStringList;
  S4StringList_D1, S4StringList_D2, S4StringList_D3: TStringList;
  S5StringList_D1, S5StringList_D2, S5StringList_D3: TStringList;
  SSALL1StringList, SSALL2StringList, SSALL3StringList, SSALL4StringList,
    SSALL5StringList: TStringList;
  Kc1StringList, Kc2StringList, Kc3StringList, Kc4StringList,
    Kc5StringList: TStringList;
  k: Integer;
  SD1, SD2, SD3: Array of Integer;
  D1, D2, D3: Array of Integer;
  MATERIALFZID1, MATERIALFZID2, MATERIALFZID3, MATERIALFZID4,
    MATERIALFZID5: Integer;
  cmtypenum1, cmtypenum2, cmtypenum3, cmtypenum4, cmtypenum5: Integer;
begin

  SSALL1StringList := TStringList.Create;
  Kc1StringList := TStringList.Create;

  MATERIALFZID1 := SSALL1[13];
  cmtypenum1 := SSALL1[14];
  if MATERIALFZID1 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL1StringList.Add(IntToStr(SSALL1[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc1StringList.Add(IntToStr(KS1[k]));
    end;

    FillGridEmptyDetail(SSALL1StringList, Kc1StringList, MATERIALFZID1,
      cmtypenum1);
  end;

  SSALL2StringList := TStringList.Create;
  Kc2StringList := TStringList.Create;

  MATERIALFZID2 := SSALL2[13];
  cmtypenum2 := SSALL2[14];
  if MATERIALFZID2 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL2StringList.Add(IntToStr(SSALL2[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc2StringList.Add(IntToStr(KS2[k]));
    end;

    FillGridEmptyDetail(SSALL2StringList, Kc2StringList, MATERIALFZID2,
      cmtypenum2);
  end;

  SSALL3StringList := TStringList.Create;
  Kc3StringList := TStringList.Create;

  MATERIALFZID3 := SSALL3[13];
  cmtypenum3 := SSALL3[14];
  if MATERIALFZID3 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL3StringList.Add(IntToStr(SSALL3[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc3StringList.Add(IntToStr(KS3[k]));
    end;

    FillGridEmptyDetail(SSALL3StringList, Kc3StringList, MATERIALFZID3,
      cmtypenum3);
  end;

  SSALL4StringList := TStringList.Create;
  Kc4StringList := TStringList.Create;

  MATERIALFZID4 := SSALL4[13];
  cmtypenum4 := SSALL4[14];
  if MATERIALFZID4 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL4StringList.Add(IntToStr(SSALL4[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc4StringList.Add(IntToStr(KS4[k]));
    end;

    FillGridEmptyDetail(SSALL4StringList, Kc4StringList, MATERIALFZID4,
      cmtypenum4);
  end;

  SSALL5StringList := TStringList.Create;
  Kc5StringList := TStringList.Create;

  MATERIALFZID5 := SSALL5[13];
  cmtypenum5 := SSALL5[14];

  if MATERIALFZID5 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL5StringList.Add(IntToStr(SSALL5[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc5StringList.Add(IntToStr(KS5[k]));
    end;

    FillGridEmptyDetail(SSALL5StringList, Kc5StringList, MATERIALFZID5,
      cmtypenum5);
  end;

end;

procedure TThreadPdCal.FillGridEmptyDetail(SAll, Kc: TStringList;
  MATERIALFZID, cmtypenum: Integer);
var
  o: Integer;
  Sum1, Sumempty: Integer;
  Sum_Kc, Sum_All: Integer;
  sequence: Integer;
  KcFlag: Boolean;
  i: Integer;
  Emptycount: Integer;
begin

  Sum1 := 0;
  Sumempty := 0;

  Sum_Kc := 0;
  Sum_All := 0;

  KcFlag := false;
  for o := 0 to CMPZSUM - 1 do
  begin
    if StrToInt(Kc[o]) > 0 then
    begin
      KcFlag := true;
      break;
    end;
  end;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := '小计';
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(SAll[o]);
      Sum_All := Sum_All + StrToInt(SAll[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(SAll[o]);
      Sum_All := Sum_All + StrToInt(SAll[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(SAll[o]);
      Sum_All := Sum_All + StrToInt(SAll[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum_All;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  // 如果有库存，加入库存数据
  if KcFlag then
  begin
    DMUtilSelect.UniQuery1.Close;
    DMUtilSelect.UniQuery1.Sql.Clear;
    DMUtilSelect.UniQuery1.Sql.Add
      ('select nextval (''cc_materialdetail'') seq');
    DMUtilSelect.UniQuery1.ExecSQL;

    if not DMUtilSelect.UniQuery1.Eof then
    begin
      sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
    end;
    DMUtilSelect.UniQuery1.Close;

    DMUtilSave.UniQuery1.Close;
    DMUtilSave.UniQuery1.Sql.Clear;
    DMUtilSave.UniQuery1.Sql.Add
      ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
      + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
    DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
    DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
    DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := '减库存';
    DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

    if cmtypenum = 1 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
          StrToInt(Kc[o]);
        Sum_Kc := Sum_Kc + StrToInt(Kc[o]);
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    if cmtypenum = 2 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
          StrToInt(Kc[o]);
        Sum_Kc := Sum_Kc + StrToInt(Kc[o]);
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    if cmtypenum = 3 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
          StrToInt(Kc[o]);
        Sum_Kc := Sum_Kc + StrToInt(Kc[o]);
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum_Kc;

    DMUtilSave.UniQuery1.ExecSQL;
    DMUtilSave.UniQuery1.Close;
  end;

  if Sum1 > 0 then
  begin

  end
  else
  begin
    Sum1 := Sum_All;
  end;

  Emptycount := (Sum1 div 1200) + 3;
  if Emptycount <= 5 then
  begin
    Emptycount := 5;
  end;

  for i := 1 to Emptycount do
  begin
    DMUtilSelect.UniQuery1.Close;
    DMUtilSelect.UniQuery1.Sql.Clear;
    DMUtilSelect.UniQuery1.Sql.Add
      ('select nextval (''cc_materialdetail'') seq');
    DMUtilSelect.UniQuery1.ExecSQL;

    if not DMUtilSelect.UniQuery1.Eof then
    begin
      sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
    end;
    DMUtilSelect.UniQuery1.Close;

    DMUtilSave.UniQuery1.Close;
    DMUtilSave.UniQuery1.Sql.Clear;
    DMUtilSave.UniQuery1.Sql.Add
      ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
      + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
    DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
    DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
    DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := i + 10;
    DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

    if cmtypenum = 1 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1 + 1))
          .Value := 0;
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    if cmtypenum = 2 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value := 0;
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    if cmtypenum = 3 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1 + 1))
          .Value := 0;
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sumempty;

    DMUtilSave.UniQuery1.ExecSQL;
    DMUtilSave.UniQuery1.Close;

  end;

  // 插入空白行
  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 0;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value := 0;
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value := 0;
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value := 0;
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sumempty;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('update cc_materialdetail set cjbc=:cjbc where MATERIALFZID=:MATERIALFZID');
  DMUtilSave.UniQuery1.ParamByName('cjbc').Value := G_bc;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('update cc_materialdetail set CJ_1=CM_1*(1+cjbc),CJ_2=CM_2*(1+cjbc),CJ_3=CM_3*(1+cjbc),CJ_4=CM_4*(1+cjbc),CJ_5=CM_5*(1+cjbc),CJ_6=CM_6*(1+cjbc),CJ_7=CM_7*(1+cjbc),CJ_8=CM_8*(1+cjbc),CJ_9=CM_9*(1+cjbc),CJ_10=CM_10*(1+cjbc),'
    + ' CJ_11=CM_11*(1+cjbc),CJ_12=CM_12*(1+cjbc),CJ_13=CM_13*(1+cjbc),CJ_14=CM_14*(1+cjbc),CJ_15=CM_15*(1+cjbc),CJ_Sum=Cm_Sum*(1+cjbc) where (CJ_Sum is null or CJ_Sum =0)');
  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

end;

procedure TThreadPdCal.FillGridEmptyNoFz;
var
  S1StringList_D1, S1StringList_D2, S1StringList_D3: TStringList;
  S2StringList_D1, S2StringList_D2, S2StringList_D3: TStringList;
  S3StringList_D1, S3StringList_D2, S3StringList_D3: TStringList;
  S4StringList_D1, S4StringList_D2, S4StringList_D3: TStringList;
  S5StringList_D1, S5StringList_D2, S5StringList_D3: TStringList;
  SSALL1StringList, SSALL2StringList, SSALL3StringList, SSALL4StringList,
    SSALL5StringList: TStringList;
  Kc1StringList, Kc2StringList, Kc3StringList, Kc4StringList,
    Kc5StringList: TStringList;
  k: Integer;
  SD1, SD2, SD3: Array of Integer;
  D1, D2, D3: Array of Integer;
  MATERIALFZID1, MATERIALFZID2, MATERIALFZID3, MATERIALFZID4,
    MATERIALFZID5: Integer;
  cmtypenum1, cmtypenum2, cmtypenum3, cmtypenum4, cmtypenum5: Integer;
begin

  SSALL1StringList := TStringList.Create;
  Kc1StringList := TStringList.Create;

  MATERIALFZID1 := SSALL1[13];
  cmtypenum1 := SSALL1[14];
  if MATERIALFZID1 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL1StringList.Add(IntToStr(SSALL1[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc1StringList.Add(IntToStr(KS1[k]));
    end;

    FillGridEmptyNoFzDetail(SSALL1StringList, Kc1StringList, MATERIALFZID1,
      cmtypenum1);
  end;

  SSALL2StringList := TStringList.Create;
  Kc2StringList := TStringList.Create;

  MATERIALFZID2 := SSALL2[13];
  cmtypenum2 := SSALL2[14];
  if MATERIALFZID2 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL2StringList.Add(IntToStr(SSALL2[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc2StringList.Add(IntToStr(KS2[k]));
    end;

    FillGridEmptyNoFzDetail(SSALL2StringList, Kc2StringList, MATERIALFZID2,
      cmtypenum2);
  end;

  SSALL3StringList := TStringList.Create;
  Kc3StringList := TStringList.Create;

  MATERIALFZID3 := SSALL3[13];
  cmtypenum3 := SSALL3[14];
  if MATERIALFZID3 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL3StringList.Add(IntToStr(SSALL3[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc3StringList.Add(IntToStr(KS3[k]));
    end;

    FillGridEmptyNoFzDetail(SSALL3StringList, Kc3StringList, MATERIALFZID3,
      cmtypenum3);
  end;

  SSALL4StringList := TStringList.Create;
  Kc4StringList := TStringList.Create;

  MATERIALFZID4 := SSALL4[13];
  cmtypenum4 := SSALL4[14];
  if MATERIALFZID4 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL4StringList.Add(IntToStr(SSALL4[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc4StringList.Add(IntToStr(KS4[k]));
    end;

    FillGridEmptyNoFzDetail(SSALL4StringList, Kc4StringList, MATERIALFZID4,
      cmtypenum4);
  end;

  SSALL5StringList := TStringList.Create;
  Kc5StringList := TStringList.Create;

  MATERIALFZID5 := SSALL5[13];
  cmtypenum5 := SSALL5[14];

  if MATERIALFZID5 > 0 then
  begin
    for k := 0 to CMPZSUM - 1 do
    begin
      SSALL5StringList.Add(IntToStr(SSALL5[k]));
    end;

    for k := 0 to CMPZSUM - 1 do
    begin
      Kc5StringList.Add(IntToStr(KS5[k]));
    end;

    FillGridEmptyNoFzDetail(SSALL5StringList, Kc5StringList, MATERIALFZID5,
      cmtypenum5);
  end;

end;

procedure TThreadPdCal.FillGridEmptyNoFzDetail(SAll, Kc: TStringList;
  MATERIALFZID, cmtypenum: Integer);
var
  o: Integer;
  Sum1, Sumempty: Integer;
  Sum_Kc, Sum_All: Integer;
  sequence: Integer;
  KcFlag: Boolean;
  i: Integer;
  Emptycount: Integer;
begin

  Sum1 := 0;
  Sumempty := 0;

  Sum_Kc := 0;
  Sum_All := 0;

  KcFlag := false;
  for o := 0 to CMPZSUM - 1 do
  begin
    if StrToInt(Kc[o]) > 0 then
    begin
      KcFlag := true;
      break;
    end;
  end;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := '小计';
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(SAll[o]);
      Sum_All := Sum_All + StrToInt(SAll[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(SAll[o]);
      Sum_All := Sum_All + StrToInt(SAll[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
        StrToInt(SAll[o]);
      Sum_All := Sum_All + StrToInt(SAll[o]);
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum_All;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  // 如果有库存，加入库存数据
  if KcFlag then
  begin
    DMUtilSelect.UniQuery1.Close;
    DMUtilSelect.UniQuery1.Sql.Clear;
    DMUtilSelect.UniQuery1.Sql.Add
      ('select nextval (''cc_materialdetail'') seq');
    DMUtilSelect.UniQuery1.ExecSQL;

    if not DMUtilSelect.UniQuery1.Eof then
    begin
      sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
    end;
    DMUtilSelect.UniQuery1.Close;

    DMUtilSave.UniQuery1.Close;
    DMUtilSave.UniQuery1.Sql.Clear;
    DMUtilSave.UniQuery1.Sql.Add
      ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
      + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
    DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
    DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
    DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := '减库存';
    DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

    if cmtypenum = 1 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
          StrToInt(Kc[o]);
        Sum_Kc := Sum_Kc + StrToInt(Kc[o]);
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    if cmtypenum = 2 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
          StrToInt(Kc[o]);
        Sum_Kc := Sum_Kc + StrToInt(Kc[o]);
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    if cmtypenum = 3 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
          StrToInt(Kc[o]);
        Sum_Kc := Sum_Kc + StrToInt(Kc[o]);
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum_Kc;

    DMUtilSave.UniQuery1.ExecSQL;
    DMUtilSave.UniQuery1.Close;
  end;

  if Sum1 > 0 then
  begin

  end
  else
  begin
    Sum1 := Sum_All;
  end;

  Emptycount := (Sum1 div 1200) + 3;
  if Emptycount <= 5 then
  begin
    Emptycount := 5;

  end;

  Sum1 := 0;
  Sumempty := 0;

  Sum_Kc := 0;
  Sum_All := 0;

  for i := 1 to 1 do
  begin
    DMUtilSelect.UniQuery1.Close;
    DMUtilSelect.UniQuery1.Sql.Clear;
    DMUtilSelect.UniQuery1.Sql.Add
      ('select nextval (''cc_materialdetail'') seq');
    DMUtilSelect.UniQuery1.ExecSQL;

    if not DMUtilSelect.UniQuery1.Eof then
    begin
      sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
    end;
    DMUtilSelect.UniQuery1.Close;

    DMUtilSave.UniQuery1.Close;
    DMUtilSave.UniQuery1.Sql.Clear;
    DMUtilSave.UniQuery1.Sql.Add
      ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
      + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
    DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
    DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
    DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := i;
    DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

    if cmtypenum = 1 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
          StrToInt(SAll[o]) - StrToInt(Kc[o]);
        Sum_All := Sum_All + StrToInt(SAll[o]) - StrToInt(Kc[o]);
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    if cmtypenum = 2 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
          StrToInt(SAll[o]) - StrToInt(Kc[o]);
        Sum_All := Sum_All + StrToInt(SAll[o]) - StrToInt(Kc[o]);
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    if cmtypenum = 3 then
    begin
      for o := 0 to CMPZSUM - 1 do
      begin
        DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value :=
          StrToInt(SAll[o]) - StrToInt(Kc[o]);
        Sum_All := Sum_All + StrToInt(SAll[o]) - StrToInt(Kc[o]);
      end;
      DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
      DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
    end;

    DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sum_All;

    DMUtilSave.UniQuery1.ExecSQL;
    DMUtilSave.UniQuery1.Close;

  end;

  // 插入空白行
  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add('select nextval (''cc_materialdetail'') seq');
  DMUtilSelect.UniQuery1.ExecSQL;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    sequence := DMUtilSelect.UniQuery1.FieldByName('seq').AsInteger;
  end;
  DMUtilSelect.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('insert into cc_materialdetail(MATERIALDETAILID,MATERIALFZID,PDNUM,CMTYPENUM,CM_SUM,CM_1,CM_2,CM_3,CM_4,CM_5,CM_6,CM_7,CM_8,CM_9,CM_10,CM_11,CM_12,CM_13,CM_14,CM_15)  '
    + ' values(:MATERIALDETAILID,:MATERIALFZID,:PDNUM,:CMTYPENUM,:CM_SUM,:CM_1,:CM_2,:CM_3,:CM_4,:CM_5,:CM_6,:CM_7,:CM_8,:CM_9,:CM_10,:CM_11,:CM_12,:CM_13,:CM_14,:CM_15)');
  DMUtilSave.UniQuery1.ParamByName('MATERIALDETAILID').Value := sequence;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ParamByName('PDNUM').Value := 0;
  DMUtilSave.UniQuery1.ParamByName('CMTYPENUM').Value := cmtypenum;

  if cmtypenum = 1 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value := 0;
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 2 then
  begin
    for o := 0 to 9 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value := 0;
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  if cmtypenum = 3 then
  begin
    for o := 0 to CMPZSUM - 1 do
    begin
      DMUtilSave.UniQuery1.ParamByName('CM_' + IntToStr(o + 1)).Value := 0;
    end;
    DMUtilSave.UniQuery1.ParamByName('CM_14').Value := 0;
    DMUtilSave.UniQuery1.ParamByName('CM_15').Value := 0;
  end;

  DMUtilSave.UniQuery1.ParamByName('CM_SUM').Value := Sumempty;

  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('update cc_materialdetail set cjbc=:cjbc where MATERIALFZID=:MATERIALFZID');
  DMUtilSave.UniQuery1.ParamByName('cjbc').Value := G_bc;
  DMUtilSave.UniQuery1.ParamByName('MATERIALFZID').Value := MATERIALFZID;
  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

  DMUtilSave.UniQuery1.Close;
  DMUtilSave.UniQuery1.Sql.Clear;
  DMUtilSave.UniQuery1.Sql.Add
    ('update cc_materialdetail set CJ_1=CM_1*(1+cjbc),CJ_2=CM_2*(1+cjbc),CJ_3=CM_3*(1+cjbc),CJ_4=CM_4*(1+cjbc),CJ_5=CM_5*(1+cjbc),CJ_6=CM_6*(1+cjbc),CJ_7=CM_7*(1+cjbc),CJ_8=CM_8*(1+cjbc),CJ_9=CM_9*(1+cjbc),CJ_10=CM_10*(1+cjbc),'
    + ' CJ_11=CM_11*(1+cjbc),CJ_12=CM_12*(1+cjbc),CJ_13=CM_13*(1+cjbc),CJ_14=CM_14*(1+cjbc),CJ_15=CM_15*(1+cjbc),CJ_Sum=Cm_Sum*(1+cjbc) where (CJ_Sum is null or CJ_Sum =0)');
  DMUtilSave.UniQuery1.ExecSQL;
  DMUtilSave.UniQuery1.Close;

end;

procedure TThreadPdCal.FillOnce(ListIn: TList);
var
  ListFilter1, ListFilter2, ListFilter3, ListFilterAll: TList;
  js: Integer;
  DsMin: Integer;
  DsItem: Integer;
  q: Integer;
  AveFlag: Boolean;
  i: Integer;
  StringList_S, StringList_D, StringList_D1, StringList_D2,
    StringList_D3: TStringList;
  DCountMin, DCountMinIndex, DCountMinIndex1: Integer;
  CalDCountSum, CalDcount1, CalDCount2, CalDCount3: Integer;
  DCount: Integer;
  List, ListFormat, ListFormatShow: TList;
  ItemIndex: Array of Integer;
  ItemCount: Integer;
  UsedIndex: Array of Integer;
  o: Integer;
begin
  ListFormat := TList.Create;

  ListFormatShow := TList.Create;

  DsMin := 0;
  ListFilter1 := TList.Create;
  ListFilter2 := TList.Create;
  ListFilter3 := TList.Create;
  ListFilterAll := TList.Create;
  for i := 0 to (ListIn.Count div 4) - 1 do
  begin
    DsItem := 0;
    StringList_D := TStringList(ListIn.Items[4 * i + 0]);
    StringList_D1 := TStringList(ListIn.Items[4 * i + 1]);
    StringList_D2 := TStringList(ListIn.Items[4 * i + 2]);
    StringList_D3 := TStringList(ListIn.Items[4 * i + 3]);

    // 优化：使用批量求和替代重复的StrToInt调用
    if TOptimizationHelper.CalculateStringListSum(StringList_D1, 0, CMPZSUM) > 0 then
    begin
      DsItem := DsItem + 1;
    end;

    if TOptimizationHelper.CalculateStringListSum(StringList_D2, 0, CMPZSUM) > 0 then
    begin
      DsItem := DsItem + 1;
    end;

    if TOptimizationHelper.CalculateStringListSum(StringList_D3, 0, CMPZSUM) > 0 then
    begin
      DsItem := DsItem + 1;
    end;

    if DsMin < DsItem then
    begin
      DsMin := DsItem;
    end;

    if DsItem = 1 then
    begin
      ListFilter1.Add(StringList_D);
      ListFilter1.Add(StringList_D1);
      ListFilter1.Add(StringList_D2);
      ListFilter1.Add(StringList_D3);
    end;

    if DsItem = 2 then
    begin
      ListFilter2.Add(StringList_D);
      ListFilter2.Add(StringList_D1);
      ListFilter2.Add(StringList_D2);
      ListFilter2.Add(StringList_D3);
    end;

    if DsItem = 3 then
    begin
      ListFilter3.Add(StringList_D);
      ListFilter3.Add(StringList_D1);
      ListFilter3.Add(StringList_D2);
      ListFilter3.Add(StringList_D3);
    end;

  end;

  if ListFilter1.Count > 0 then
  begin
    for i := 0 to (ListFilter1.Count div 4) - 1 do
    begin
      StringList_D := TStringList(ListFilter1.Items[4 * i + 0]);
      StringList_D1 := TStringList(ListFilter1.Items[4 * i + 1]);
      StringList_D2 := TStringList(ListFilter1.Items[4 * i + 2]);
      StringList_D3 := TStringList(ListFilter1.Items[4 * i + 3]);
      ListFilterAll.Add(StringList_D);
      ListFilterAll.Add(StringList_D1);
      ListFilterAll.Add(StringList_D2);
      ListFilterAll.Add(StringList_D3);
    end;
  end
  else
  begin
    if ListFilter2.Count > 0 then
    begin
      for i := 0 to (ListFilter2.Count div 4) - 1 do
      begin
        StringList_D := TStringList(ListFilter2.Items[4 * i + 0]);
        StringList_D1 := TStringList(ListFilter2.Items[4 * i + 1]);
        StringList_D2 := TStringList(ListFilter2.Items[4 * i + 2]);
        StringList_D3 := TStringList(ListFilter2.Items[4 * i + 3]);
        ListFilterAll.Add(StringList_D);
        ListFilterAll.Add(StringList_D1);
        ListFilterAll.Add(StringList_D2);
        ListFilterAll.Add(StringList_D3);
      end;
    end
    else
    begin
      if ListFilter3.Count > 0 then
      begin
        for i := 0 to (ListFilter3.Count div 4) - 1 do
        begin
          StringList_D := TStringList(ListFilter3.Items[4 * i + 0]);
          StringList_D1 := TStringList(ListFilter3.Items[4 * i + 1]);
          StringList_D2 := TStringList(ListFilter3.Items[4 * i + 2]);
          StringList_D3 := TStringList(ListFilter3.Items[4 * i + 3]);
          ListFilterAll.Add(StringList_D);
          ListFilterAll.Add(StringList_D1);
          ListFilterAll.Add(StringList_D2);
          ListFilterAll.Add(StringList_D3);
        end;
      end;
    end;
  end;

  // 排序
  DCountMin := 9999;
  DCountMinIndex := 0;
  DCountMinIndex1 := -1;
  for i := 0 to (ListFilterAll.Count div 4) - 1 do
  begin
    StringList_S := TStringList(ListFilterAll.Items[4 * i + 0]);
    StringList_D1 := TStringList(ListFilterAll.Items[4 * i + 1]);
    StringList_D2 := TStringList(ListFilterAll.Items[4 * i + 2]);
    StringList_D3 := TStringList(ListFilterAll.Items[4 * i + 3]);
    DCount := StrToInt(StringList_S[13]);
    if DCount < DCountMin then
    begin
      if DCountMinIndex = 0 then
      begin
        DCountMin := DCount;
        DCountMinIndex := i;
      end
      else
      begin
        DCountMinIndex1 := DCountMinIndex;
        DCountMin := DCount;
        DCountMinIndex := i;
      end;

    end;

  end;

  for i := 0 to (ListFilterAll.Count div 4) - 1 do
  begin
    if i = DCountMinIndex then
    begin
      StringList_S := TStringList(ListFilterAll.Items[4 * i + 0]);
      StringList_D1 := TStringList(ListFilterAll.Items[4 * i + 1]);
      StringList_D2 := TStringList(ListFilterAll.Items[4 * i + 2]);
      StringList_D3 := TStringList(ListFilterAll.Items[4 * i + 3]);
      ListFormat.Add(StringList_S);
      ListFormat.Add(StringList_D1);
      ListFormat.Add(StringList_D2);
      ListFormat.Add(StringList_D3);
      break;
    end;
  end;

  for i := 0 to (ListFormat.Count div 4) - 1 do
  begin
    StringList_S := TStringList(ListFormat.Items[4 * i + 0]);
    StringList_D1 := TStringList(ListFormat.Items[4 * i + 1]);
    StringList_D2 := TStringList(ListFormat.Items[4 * i + 2]);
    StringList_D3 := TStringList(ListFormat.Items[4 * i + 3]);
    ListFormatShow.Add(StringList_S);
    setlength(ItemIndex, 3);
    ItemIndex[0] := TOptimizationHelper.GetCachedIntValue(StringList_D1[14]);
    ItemIndex[1] := TOptimizationHelper.GetCachedIntValue(StringList_D2[14]);
    ItemIndex[2] := TOptimizationHelper.GetCachedIntValue(StringList_D3[14]);
    if MoreKsFlag = false then
    begin
      BubbleSort(ItemIndex);
    end;

    setlength(UsedIndex, 3);
    UsedIndex[0] := -1;
    UsedIndex[1] := -1;
    UsedIndex[2] := -1;

    for o := 0 to 2 do
    begin
      if (StrToInt(StringList_D1[14]) = ItemIndex[o]) and (UsedIndex[0] = -1)
      then
      begin
        ListFormatShow.Add(StringList_D1);
        UsedIndex[0] := 1;
      end
      else if (StrToInt(StringList_D2[14]) = ItemIndex[o]) and
        (UsedIndex[1] = -1) then
      begin
        ListFormatShow.Add(StringList_D2);
        UsedIndex[1] := 2;
      end
      else if (StrToInt(StringList_D3[14]) = ItemIndex[o]) and
        (UsedIndex[2] = -1) then
      begin
        ListFormatShow.Add(StringList_D3);
        UsedIndex[2] := 3;
      end
    end;
  end;

  for i := 0 to (ListFormatShow.Count div 4) - 1 do
  begin
    if i = 0 then
    begin
      FillGrid2(TStringList(ListFormatShow.Items[4 * i + 0]),
        TStringList(ListFormatShow.Items[4 * i + 1]),
        TStringList(ListFormatShow.Items[4 * i + 2]),
        TStringList(ListFormatShow.Items[4 * i + 3]));
    end;
  end;

end;

procedure TThreadPdCal.FillOnceFive(ListIn: TList);
var
  ListFilter1, ListFilter2, ListFilter3, ListFilter4, ListFilter5,
    ListFilterAll: TList;
  js: Integer;
  DsMin: Integer;
  DsItem: Integer;
  q: Integer;
  AveFlag: Boolean;
  i: Integer;
  StringList_S, StringList_D, StringList_D1, StringList_D2, StringList_D3,
    StringList_D4, StringList_D5: TStringList;
  DCountMin, DCountMinIndex, DCountMinIndex1: Integer;
  CalDCountSum, CalDcount1, CalDCount2, CalDCount3, CalDCount4,
    CalDCount5: Integer;
  DCount: Integer;
  List, ListFormat, ListFormatShow: TList;
  ItemIndex: Array of Integer;
  ItemCount: Integer;
  UsedIndex: Array of Integer;
  o: Integer;
begin
  ListFormat := TList.Create;

  ListFormatShow := TList.Create;

  DsMin := 0;
  ListFilter1 := TList.Create;
  ListFilter2 := TList.Create;
  ListFilter3 := TList.Create;
  ListFilter4 := TList.Create;
  ListFilter5 := TList.Create;
  ListFilterAll := TList.Create;
  for i := 0 to (ListIn.Count div 6) - 1 do
  begin
    DsItem := 0;
    StringList_D := TStringList(ListIn.Items[6 * i + 0]);
    StringList_D1 := TStringList(ListIn.Items[6 * i + 1]);
    StringList_D2 := TStringList(ListIn.Items[6 * i + 2]);
    StringList_D3 := TStringList(ListIn.Items[6 * i + 3]);
    StringList_D4 := TStringList(ListIn.Items[6 * i + 4]);
    StringList_D5 := TStringList(ListIn.Items[6 * i + 5]);
    // 优化：使用批量求和替代重复的StrToInt调用
    if TOptimizationHelper.CalculateStringListSum(StringList_D1, 0, CMPZSUM) > 0 then
    begin
      DsItem := DsItem + 1;
    end;

    if TOptimizationHelper.CalculateStringListSum(StringList_D2, 0, CMPZSUM) > 0 then
    begin
      DsItem := DsItem + 1;
    end;

    if TOptimizationHelper.CalculateStringListSum(StringList_D3, 0, CMPZSUM) > 0 then
    begin
      DsItem := DsItem + 1;
    end;

    if TOptimizationHelper.CalculateStringListSum(StringList_D4, 0, CMPZSUM) > 0 then
    begin
      DsItem := DsItem + 1;
    end;

    if TOptimizationHelper.CalculateStringListSum(StringList_D5, 0, CMPZSUM) > 0 then
    begin
      DsItem := DsItem + 1;
    end;

    if DsMin < DsItem then
    begin
      DsMin := DsItem;
    end;

    if DsItem = 1 then
    begin
      ListFilter1.Add(StringList_D);
      ListFilter1.Add(StringList_D1);
      ListFilter1.Add(StringList_D2);
      ListFilter1.Add(StringList_D3);
      ListFilter1.Add(StringList_D4);
      ListFilter1.Add(StringList_D5);
    end;

    if DsItem = 2 then
    begin
      ListFilter2.Add(StringList_D);
      ListFilter2.Add(StringList_D1);
      ListFilter2.Add(StringList_D2);
      ListFilter2.Add(StringList_D3);
      ListFilter2.Add(StringList_D4);
      ListFilter2.Add(StringList_D5);
    end;

    if DsItem = 3 then
    begin
      ListFilter3.Add(StringList_D);
      ListFilter3.Add(StringList_D1);
      ListFilter3.Add(StringList_D2);
      ListFilter3.Add(StringList_D3);
      ListFilter3.Add(StringList_D4);
      ListFilter3.Add(StringList_D5);
    end;

    if DsItem = 4 then
    begin
      ListFilter4.Add(StringList_D);
      ListFilter4.Add(StringList_D1);
      ListFilter4.Add(StringList_D2);
      ListFilter4.Add(StringList_D3);
      ListFilter4.Add(StringList_D4);
      ListFilter4.Add(StringList_D5);
    end;

    if DsItem = 5 then
    begin
      ListFilter5.Add(StringList_D);
      ListFilter5.Add(StringList_D1);
      ListFilter5.Add(StringList_D2);
      ListFilter5.Add(StringList_D3);
      ListFilter5.Add(StringList_D4);
      ListFilter5.Add(StringList_D5);
    end;

  end;

  if ListFilter1.Count > 0 then
  begin
    for i := 0 to (ListFilter1.Count div 6) - 1 do
    begin
      StringList_D := TStringList(ListFilter1.Items[6 * i + 0]);
      StringList_D1 := TStringList(ListFilter1.Items[6 * i + 1]);
      StringList_D2 := TStringList(ListFilter1.Items[6 * i + 2]);
      StringList_D3 := TStringList(ListFilter1.Items[6 * i + 3]);
      StringList_D4 := TStringList(ListFilter1.Items[6 * i + 4]);
      StringList_D5 := TStringList(ListFilter1.Items[6 * i + 5]);
      ListFilterAll.Add(StringList_D);
      ListFilterAll.Add(StringList_D1);
      ListFilterAll.Add(StringList_D2);
      ListFilterAll.Add(StringList_D3);
      ListFilterAll.Add(StringList_D4);
      ListFilterAll.Add(StringList_D5);
    end;
  end
  else
  begin
    if ListFilter2.Count > 0 then
    begin
      for i := 0 to (ListFilter2.Count div 6) - 1 do
      begin
        StringList_D := TStringList(ListFilter2.Items[6 * i + 0]);
        StringList_D1 := TStringList(ListFilter2.Items[6 * i + 1]);
        StringList_D2 := TStringList(ListFilter2.Items[6 * i + 2]);
        StringList_D3 := TStringList(ListFilter2.Items[6 * i + 3]);
        StringList_D4 := TStringList(ListFilter2.Items[6 * i + 4]);
        StringList_D5 := TStringList(ListFilter2.Items[6 * i + 5]);
        ListFilterAll.Add(StringList_D);
        ListFilterAll.Add(StringList_D1);
        ListFilterAll.Add(StringList_D2);
        ListFilterAll.Add(StringList_D3);
        ListFilterAll.Add(StringList_D4);
        ListFilterAll.Add(StringList_D5);
      end;
    end
    else
    begin
      if ListFilter3.Count > 0 then
      begin
        for i := 0 to (ListFilter3.Count div 6) - 1 do
        begin
          StringList_D := TStringList(ListFilter3.Items[6 * i + 0]);
          StringList_D1 := TStringList(ListFilter3.Items[6 * i + 1]);
          StringList_D2 := TStringList(ListFilter3.Items[6 * i + 2]);
          StringList_D3 := TStringList(ListFilter3.Items[6 * i + 3]);
          StringList_D4 := TStringList(ListFilter3.Items[6 * i + 4]);
          StringList_D5 := TStringList(ListFilter3.Items[6 * i + 5]);
          ListFilterAll.Add(StringList_D);
          ListFilterAll.Add(StringList_D1);
          ListFilterAll.Add(StringList_D2);
          ListFilterAll.Add(StringList_D3);
          ListFilterAll.Add(StringList_D4);
          ListFilterAll.Add(StringList_D5);
        end;
      end
      else
      begin
        if ListFilter4.Count > 0 then
        begin
          for i := 0 to (ListFilter4.Count div 6) - 1 do
          begin
            StringList_D := TStringList(ListFilter4.Items[6 * i + 0]);
            StringList_D1 := TStringList(ListFilter4.Items[6 * i + 1]);
            StringList_D2 := TStringList(ListFilter4.Items[6 * i + 2]);
            StringList_D3 := TStringList(ListFilter4.Items[6 * i + 3]);
            StringList_D4 := TStringList(ListFilter4.Items[6 * i + 4]);
            StringList_D5 := TStringList(ListFilter4.Items[6 * i + 5]);
            ListFilterAll.Add(StringList_D);
            ListFilterAll.Add(StringList_D1);
            ListFilterAll.Add(StringList_D2);
            ListFilterAll.Add(StringList_D3);
            ListFilterAll.Add(StringList_D4);
            ListFilterAll.Add(StringList_D5);
          end;
        end
        else
        begin
          if ListFilter5.Count > 0 then
          begin
            for i := 0 to (ListFilter5.Count div 6) - 1 do
            begin
              StringList_D := TStringList(ListFilter5.Items[6 * i + 0]);
              StringList_D1 := TStringList(ListFilter5.Items[6 * i + 1]);
              StringList_D2 := TStringList(ListFilter5.Items[6 * i + 2]);
              StringList_D3 := TStringList(ListFilter5.Items[6 * i + 3]);
              StringList_D4 := TStringList(ListFilter5.Items[6 * i + 4]);
              StringList_D5 := TStringList(ListFilter5.Items[6 * i + 5]);
              ListFilterAll.Add(StringList_D);
              ListFilterAll.Add(StringList_D1);
              ListFilterAll.Add(StringList_D2);
              ListFilterAll.Add(StringList_D3);
              ListFilterAll.Add(StringList_D4);
              ListFilterAll.Add(StringList_D5);
            end;
          end
        end;
      end;
    end;
  end;

  // 排序
  DCountMin := 9999;
  DCountMinIndex := 0;
  DCountMinIndex1 := -1;
  for i := 0 to (ListFilterAll.Count div 6) - 1 do
  begin
    StringList_S := TStringList(ListFilterAll.Items[6 * i + 0]);
    StringList_D1 := TStringList(ListFilterAll.Items[6 * i + 1]);
    StringList_D2 := TStringList(ListFilterAll.Items[6 * i + 2]);
    StringList_D3 := TStringList(ListFilterAll.Items[6 * i + 3]);
    StringList_D4 := TStringList(ListFilterAll.Items[6 * i + 4]);
    StringList_D5 := TStringList(ListFilterAll.Items[6 * i + 5]);

    DCount := StrToInt(StringList_S[13]);
    if DCount < DCountMin then
    begin
      if DCountMinIndex = 0 then
      begin
        DCountMin := DCount;
        DCountMinIndex := i;
      end
      else
      begin
        DCountMinIndex1 := DCountMinIndex;
        DCountMin := DCount;
        DCountMinIndex := i;
      end;

    end;

  end;

  for i := 0 to (ListFilterAll.Count div 6) - 1 do
  begin
    if i = DCountMinIndex then
    begin
      StringList_S := TStringList(ListFilterAll.Items[6 * i + 0]);
      StringList_D1 := TStringList(ListFilterAll.Items[6 * i + 1]);
      StringList_D2 := TStringList(ListFilterAll.Items[6 * i + 2]);
      StringList_D3 := TStringList(ListFilterAll.Items[6 * i + 3]);
      StringList_D4 := TStringList(ListFilterAll.Items[6 * i + 4]);
      StringList_D5 := TStringList(ListFilterAll.Items[6 * i + 5]);
      ListFormat.Add(StringList_S);
      ListFormat.Add(StringList_D1);
      ListFormat.Add(StringList_D2);
      ListFormat.Add(StringList_D3);
      ListFormat.Add(StringList_D4);
      ListFormat.Add(StringList_D5);
      break;
    end;
  end;

  for i := 0 to (ListFormat.Count div 6) - 1 do
  begin
    StringList_S := TStringList(ListFormat.Items[6 * i + 0]);
    StringList_D1 := TStringList(ListFormat.Items[6 * i + 1]);
    StringList_D2 := TStringList(ListFormat.Items[6 * i + 2]);
    StringList_D3 := TStringList(ListFormat.Items[6 * i + 3]);
    StringList_D4 := TStringList(ListFormat.Items[6 * i + 4]);
    StringList_D5 := TStringList(ListFormat.Items[6 * i + 5]);
    ListFormatShow.Add(StringList_S);
    setlength(ItemIndex, 5);
    ItemIndex[0] := TOptimizationHelper.GetCachedIntValue(StringList_D1[14]);
    ItemIndex[1] := TOptimizationHelper.GetCachedIntValue(StringList_D2[14]);
    ItemIndex[2] := TOptimizationHelper.GetCachedIntValue(StringList_D3[14]);
    ItemIndex[3] := TOptimizationHelper.GetCachedIntValue(StringList_D4[14]);
    ItemIndex[4] := TOptimizationHelper.GetCachedIntValue(StringList_D5[14]);
    BubbleSort(ItemIndex);

    setlength(UsedIndex, 5);
    UsedIndex[0] := -1;
    UsedIndex[1] := -1;
    UsedIndex[2] := -1;
    UsedIndex[3] := -1;
    UsedIndex[4] := -1;

    for o := 0 to 4 do
    begin
      if (StrToInt(StringList_D1[14]) = ItemIndex[o]) and (UsedIndex[0] = -1)
      then
      begin
        ListFormatShow.Add(StringList_D1);
        UsedIndex[0] := 1;
      end
      else if (StrToInt(StringList_D2[14]) = ItemIndex[o]) and
        (UsedIndex[1] = -1) then
      begin
        ListFormatShow.Add(StringList_D2);
        UsedIndex[1] := 2;
      end
      else if (StrToInt(StringList_D3[14]) = ItemIndex[o]) and
        (UsedIndex[2] = -1) then
      begin
        ListFormatShow.Add(StringList_D3);
        UsedIndex[2] := 3;
      end
      else if (StrToInt(StringList_D4[14]) = ItemIndex[o]) and
        (UsedIndex[3] = -1) then
      begin
        ListFormatShow.Add(StringList_D4);
        UsedIndex[3] := 4;
      end
      else if (StrToInt(StringList_D5[14]) = ItemIndex[o]) and
        (UsedIndex[4] = -1) then
      begin
        ListFormatShow.Add(StringList_D5);
        UsedIndex[4] := 5;
      end
    end;
  end;

  for i := 0 to (ListFormatShow.Count div 6) - 1 do
  begin
    if i = 0 then
    begin
      FillGrid2Five(TStringList(ListFormatShow.Items[6 * i + 0]),
        TStringList(ListFormatShow.Items[6 * i + 1]),
        TStringList(ListFormatShow.Items[6 * i + 2]),
        TStringList(ListFormatShow.Items[6 * i + 3]),
        TStringList(ListFormatShow.Items[6 * i + 4]),
        TStringList(ListFormatShow.Items[6 * i + 5]));
    end;

  end;

end;

function TThreadPdCal.FillOnceFiveReturn(ListIn: TList;
  FlagType: Integer): TList;
var
  ListFilter1, ListFilter2, ListFilter3, ListFilter4, ListFilter5,
    ListFilterAll: TList;
  js: Integer;
  DsMin: Integer;
  DsItem: Integer;
  q: Integer;
  AveFlag: Boolean;
  i: Integer;
  StringList_S, StringList_D, StringList_D1, StringList_D2, StringList_D3,
    StringList_D4, StringList_D5: TStringList;
  DCountMin, DCountMinIndex, DCountMinIndex1: Integer;
  CalDCountSum, CalDcount1, CalDCount2, CalDCount3, CalDCount4,
    CalDCount5: Integer;
  DCount: Integer;
  List, ListFormat, ListFormatShow: TList;
  ItemIndex: Array of Integer;
  ItemCount: Integer;
  UsedIndex: Array of Integer;
  o: Integer;
  ReturnList: TList;
begin
  ListFormat := TList.Create;

  ListFormatShow := TList.Create;

  DsMin := 0;
  ListFilter1 := TList.Create;
  ListFilter2 := TList.Create;
  ListFilter3 := TList.Create;
  ListFilter4 := TList.Create;
  ListFilter5 := TList.Create;
  ListFilterAll := TList.Create;
  for i := 0 to (ListIn.Count div 6) - 1 do
  begin
    DsItem := 0;
    StringList_D := TStringList(ListIn.Items[6 * i + 0]);
    StringList_D1 := TStringList(ListIn.Items[6 * i + 1]);
    StringList_D2 := TStringList(ListIn.Items[6 * i + 2]);
    StringList_D3 := TStringList(ListIn.Items[6 * i + 3]);
    StringList_D4 := TStringList(ListIn.Items[6 * i + 4]);
    StringList_D5 := TStringList(ListIn.Items[6 * i + 5]);
    // 优化：使用批量求和替代重复的StrToInt调用
    if TOptimizationHelper.CalculateStringListSum(StringList_D1, 0, CMPZSUM) > 0 then
    begin
      DsItem := DsItem + 1;
    end;

    if TOptimizationHelper.CalculateStringListSum(StringList_D2, 0, CMPZSUM) > 0 then
    begin
      DsItem := DsItem + 1;
    end;

    if TOptimizationHelper.CalculateStringListSum(StringList_D3, 0, CMPZSUM) > 0 then
    begin
      DsItem := DsItem + 1;
    end;

    if TOptimizationHelper.CalculateStringListSum(StringList_D4, 0, CMPZSUM) > 0 then
    begin
      DsItem := DsItem + 1;
    end;

    if TOptimizationHelper.CalculateStringListSum(StringList_D5, 0, CMPZSUM) > 0 then
    begin
      DsItem := DsItem + 1;
    end;

    if DsMin < DsItem then
    begin
      DsMin := DsItem;
    end;

    if DsItem = 1 then
    begin
      ListFilter1.Add(StringList_D);
      ListFilter1.Add(StringList_D1);
      ListFilter1.Add(StringList_D2);
      ListFilter1.Add(StringList_D3);
      ListFilter1.Add(StringList_D4);
      ListFilter1.Add(StringList_D5);
    end;

    if DsItem = 2 then
    begin
      ListFilter2.Add(StringList_D);
      ListFilter2.Add(StringList_D1);
      ListFilter2.Add(StringList_D2);
      ListFilter2.Add(StringList_D3);
      ListFilter2.Add(StringList_D4);
      ListFilter2.Add(StringList_D5);
    end;

    if DsItem = 3 then
    begin
      ListFilter3.Add(StringList_D);
      ListFilter3.Add(StringList_D1);
      ListFilter3.Add(StringList_D2);
      ListFilter3.Add(StringList_D3);
      ListFilter3.Add(StringList_D4);
      ListFilter3.Add(StringList_D5);
    end;

    if DsItem = 4 then
    begin
      ListFilter4.Add(StringList_D);
      ListFilter4.Add(StringList_D1);
      ListFilter4.Add(StringList_D2);
      ListFilter4.Add(StringList_D3);
      ListFilter4.Add(StringList_D4);
      ListFilter4.Add(StringList_D5);
    end;

    if DsItem = 5 then
    begin
      ListFilter5.Add(StringList_D);
      ListFilter5.Add(StringList_D1);
      ListFilter5.Add(StringList_D2);
      ListFilter5.Add(StringList_D3);
      ListFilter5.Add(StringList_D4);
      ListFilter5.Add(StringList_D5);
    end;

  end;

  if ListFilter1.Count > 0 then
  begin
    for i := 0 to (ListFilter1.Count div 6) - 1 do
    begin
      StringList_D := TStringList(ListFilter1.Items[6 * i + 0]);
      StringList_D1 := TStringList(ListFilter1.Items[6 * i + 1]);
      StringList_D2 := TStringList(ListFilter1.Items[6 * i + 2]);
      StringList_D3 := TStringList(ListFilter1.Items[6 * i + 3]);
      StringList_D4 := TStringList(ListFilter1.Items[6 * i + 4]);
      StringList_D5 := TStringList(ListFilter1.Items[6 * i + 5]);
      ListFilterAll.Add(StringList_D);
      ListFilterAll.Add(StringList_D1);
      ListFilterAll.Add(StringList_D2);
      ListFilterAll.Add(StringList_D3);
      ListFilterAll.Add(StringList_D4);
      ListFilterAll.Add(StringList_D5);
    end;
  end
  else
  begin
    if ListFilter2.Count > 0 then
    begin
      for i := 0 to (ListFilter2.Count div 6) - 1 do
      begin
        StringList_D := TStringList(ListFilter2.Items[6 * i + 0]);
        StringList_D1 := TStringList(ListFilter2.Items[6 * i + 1]);
        StringList_D2 := TStringList(ListFilter2.Items[6 * i + 2]);
        StringList_D3 := TStringList(ListFilter2.Items[6 * i + 3]);
        StringList_D4 := TStringList(ListFilter2.Items[6 * i + 4]);
        StringList_D5 := TStringList(ListFilter2.Items[6 * i + 5]);
        ListFilterAll.Add(StringList_D);
        ListFilterAll.Add(StringList_D1);
        ListFilterAll.Add(StringList_D2);
        ListFilterAll.Add(StringList_D3);
        ListFilterAll.Add(StringList_D4);
        ListFilterAll.Add(StringList_D5);
      end;
    end
    else
    begin
      if ListFilter3.Count > 0 then
      begin
        for i := 0 to (ListFilter3.Count div 6) - 1 do
        begin
          StringList_D := TStringList(ListFilter3.Items[6 * i + 0]);
          StringList_D1 := TStringList(ListFilter3.Items[6 * i + 1]);
          StringList_D2 := TStringList(ListFilter3.Items[6 * i + 2]);
          StringList_D3 := TStringList(ListFilter3.Items[6 * i + 3]);
          StringList_D4 := TStringList(ListFilter3.Items[6 * i + 4]);
          StringList_D5 := TStringList(ListFilter3.Items[6 * i + 5]);
          ListFilterAll.Add(StringList_D);
          ListFilterAll.Add(StringList_D1);
          ListFilterAll.Add(StringList_D2);
          ListFilterAll.Add(StringList_D3);
          ListFilterAll.Add(StringList_D4);
          ListFilterAll.Add(StringList_D5);
        end;
      end
      else
      begin
        if ListFilter4.Count > 0 then
        begin
          for i := 0 to (ListFilter4.Count div 6) - 1 do
          begin
            StringList_D := TStringList(ListFilter4.Items[6 * i + 0]);
            StringList_D1 := TStringList(ListFilter4.Items[6 * i + 1]);
            StringList_D2 := TStringList(ListFilter4.Items[6 * i + 2]);
            StringList_D3 := TStringList(ListFilter4.Items[6 * i + 3]);
            StringList_D4 := TStringList(ListFilter4.Items[6 * i + 4]);
            StringList_D5 := TStringList(ListFilter4.Items[6 * i + 5]);
            ListFilterAll.Add(StringList_D);
            ListFilterAll.Add(StringList_D1);
            ListFilterAll.Add(StringList_D2);
            ListFilterAll.Add(StringList_D3);
            ListFilterAll.Add(StringList_D4);
            ListFilterAll.Add(StringList_D5);
          end;
        end
        else
        begin
          if ListFilter5.Count > 0 then
          begin
            for i := 0 to (ListFilter5.Count div 6) - 1 do
            begin
              StringList_D := TStringList(ListFilter5.Items[6 * i + 0]);
              StringList_D1 := TStringList(ListFilter5.Items[6 * i + 1]);
              StringList_D2 := TStringList(ListFilter5.Items[6 * i + 2]);
              StringList_D3 := TStringList(ListFilter5.Items[6 * i + 3]);
              StringList_D4 := TStringList(ListFilter5.Items[6 * i + 4]);
              StringList_D5 := TStringList(ListFilter5.Items[6 * i + 5]);
              ListFilterAll.Add(StringList_D);
              ListFilterAll.Add(StringList_D1);
              ListFilterAll.Add(StringList_D2);
              ListFilterAll.Add(StringList_D3);
              ListFilterAll.Add(StringList_D4);
              ListFilterAll.Add(StringList_D5);
            end;
          end
        end;
      end;
    end;
  end;

  // 排序
  DCountMin := 9999;
  DCountMinIndex := 0;
  DCountMinIndex1 := -1;
  for i := 0 to (ListFilterAll.Count div 6) - 1 do
  begin
    StringList_S := TStringList(ListFilterAll.Items[6 * i + 0]);
    StringList_D1 := TStringList(ListFilterAll.Items[6 * i + 1]);
    StringList_D2 := TStringList(ListFilterAll.Items[6 * i + 2]);
    StringList_D3 := TStringList(ListFilterAll.Items[6 * i + 3]);
    StringList_D4 := TStringList(ListFilterAll.Items[6 * i + 4]);
    StringList_D5 := TStringList(ListFilterAll.Items[6 * i + 5]);

    DCount := StrToInt(StringList_S[13]);
    if DCount < DCountMin then
    begin
      if DCountMinIndex = 0 then
      begin
        DCountMin := DCount;
        DCountMinIndex := i;
      end
      else
      begin
        DCountMinIndex1 := DCountMinIndex;
        DCountMin := DCount;
        DCountMinIndex := i;
      end;

    end;

  end;

  for i := 0 to (ListFilterAll.Count div 6) - 1 do
  begin
    if i = DCountMinIndex then
    begin
      StringList_S := TStringList(ListFilterAll.Items[6 * i + 0]);
      StringList_D1 := TStringList(ListFilterAll.Items[6 * i + 1]);
      StringList_D2 := TStringList(ListFilterAll.Items[6 * i + 2]);
      StringList_D3 := TStringList(ListFilterAll.Items[6 * i + 3]);
      StringList_D4 := TStringList(ListFilterAll.Items[6 * i + 4]);
      StringList_D5 := TStringList(ListFilterAll.Items[6 * i + 5]);
      ListFormat.Add(StringList_S);
      ListFormat.Add(StringList_D1);
      ListFormat.Add(StringList_D2);
      ListFormat.Add(StringList_D3);
      ListFormat.Add(StringList_D4);
      ListFormat.Add(StringList_D5);
      break;
    end;
  end;

  for i := 0 to (ListFormat.Count div 6) - 1 do
  begin
    StringList_S := TStringList(ListFormat.Items[6 * i + 0]);
    StringList_D1 := TStringList(ListFormat.Items[6 * i + 1]);
    StringList_D2 := TStringList(ListFormat.Items[6 * i + 2]);
    StringList_D3 := TStringList(ListFormat.Items[6 * i + 3]);
    StringList_D4 := TStringList(ListFormat.Items[6 * i + 4]);
    StringList_D5 := TStringList(ListFormat.Items[6 * i + 5]);
    ListFormatShow.Add(StringList_S);
    setlength(ItemIndex, 5);
    ItemIndex[0] := TOptimizationHelper.GetCachedIntValue(StringList_D1[14]);
    ItemIndex[1] := TOptimizationHelper.GetCachedIntValue(StringList_D2[14]);
    ItemIndex[2] := TOptimizationHelper.GetCachedIntValue(StringList_D3[14]);
    ItemIndex[3] := TOptimizationHelper.GetCachedIntValue(StringList_D4[14]);
    ItemIndex[4] := TOptimizationHelper.GetCachedIntValue(StringList_D5[14]);
    BubbleSort(ItemIndex);

    setlength(UsedIndex, 5);
    UsedIndex[0] := -1;
    UsedIndex[1] := -1;
    UsedIndex[2] := -1;
    UsedIndex[3] := -1;
    UsedIndex[4] := -1;

    for o := 0 to 4 do
    begin
      if (StrToInt(StringList_D1[14]) = ItemIndex[o]) and (UsedIndex[0] = -1)
      then
      begin
        ListFormatShow.Add(StringList_D1);
        UsedIndex[0] := 1;
      end
      else if (StrToInt(StringList_D2[14]) = ItemIndex[o]) and
        (UsedIndex[1] = -1) then
      begin
        ListFormatShow.Add(StringList_D2);
        UsedIndex[1] := 2;
      end
      else if (StrToInt(StringList_D3[14]) = ItemIndex[o]) and
        (UsedIndex[2] = -1) then
      begin
        ListFormatShow.Add(StringList_D3);
        UsedIndex[2] := 3;
      end
      else if (StrToInt(StringList_D4[14]) = ItemIndex[o]) and
        (UsedIndex[3] = -1) then
      begin
        ListFormatShow.Add(StringList_D4);
        UsedIndex[3] := 4;
      end
      else if (StrToInt(StringList_D5[14]) = ItemIndex[o]) and
        (UsedIndex[4] = -1) then
      begin
        ListFormatShow.Add(StringList_D5);
        UsedIndex[4] := 5;
      end
    end;
  end;

  ReturnList := TList.Create;
  for i := 0 to (ListFormatShow.Count div 6) - 1 do
  begin
    if i = 0 then
    begin
      if FlagType = 1 then
      begin
        ReturnList.Add(TStringList(ListFormatShow.Items[6 * i + 1]));
        ReturnList.Add(TStringList(ListFormatShow.Items[6 * i + 2]));
        ReturnList.Add(TStringList(ListFormatShow.Items[6 * i + 3]));
        ReturnList.Add(TStringList(ListFormatShow.Items[6 * i + 4]));
        ReturnList.Add(TStringList(ListFormatShow.Items[6 * i + 5]));
      end
      else
      begin
        ReturnList.Add(TStringList(ListFormatShow.Items[6 * i + 0]));
        ReturnList.Add(TStringList(ListFormatShow.Items[6 * i + 1]));
        ReturnList.Add(TStringList(ListFormatShow.Items[6 * i + 2]));
        ReturnList.Add(TStringList(ListFormatShow.Items[6 * i + 3]));
        ReturnList.Add(TStringList(ListFormatShow.Items[6 * i + 4]));
        ReturnList.Add(TStringList(ListFormatShow.Items[6 * i + 5]));
      end;

    end;

  end;

  result := ReturnList;

end;

function TThreadPdCal.FillOnceReturn(ListIn: TList; FlagType: Integer): TList;
var
  ListFilter1, ListFilter2, ListFilter3, ListFilterAll: TList;
  js: Integer;
  DsMin: Integer;
  DsItem: Integer;
  q: Integer;
  AveFlag: Boolean;
  i: Integer;
  StringList_S, StringList_D, StringList_D1, StringList_D2,
    StringList_D3: TStringList;
  DCountMin, DCountMinIndex, DCountMinIndex1: Integer;
  CalDCountSum, CalDcount1, CalDCount2, CalDCount3: Integer;
  DCount: Integer;
  List, ListFormat, ListFormatShow: TList;
  ItemIndex: Array of Integer;
  ItemCount: Integer;
  UsedIndex: Array of Integer;
  o: Integer;
  ReturnList: TList;
begin
  ListFormat := TList.Create;

  ListFormatShow := TList.Create;

  DsMin := 0;
  ListFilter1 := TList.Create;
  ListFilter2 := TList.Create;
  ListFilter3 := TList.Create;
  ListFilterAll := TList.Create;
  for i := 0 to (ListIn.Count div 4) - 1 do
  begin
    DsItem := 0;
    StringList_D := TStringList(ListIn.Items[4 * i + 0]);
    StringList_D1 := TStringList(ListIn.Items[4 * i + 1]);
    StringList_D2 := TStringList(ListIn.Items[4 * i + 2]);
    StringList_D3 := TStringList(ListIn.Items[4 * i + 3]);

    // 优化：使用批量求和替代重复的StrToInt调用
    if TOptimizationHelper.CalculateStringListSum(StringList_D1, 0, CMPZSUM) > 0 then
    begin
      DsItem := DsItem + 1;
    end;

    if TOptimizationHelper.CalculateStringListSum(StringList_D2, 0, CMPZSUM) > 0 then
    begin
      DsItem := DsItem + 1;
    end;

    if TOptimizationHelper.CalculateStringListSum(StringList_D3, 0, CMPZSUM) > 0 then
    begin
      DsItem := DsItem + 1;
    end;

    if DsMin < DsItem then
    begin
      DsMin := DsItem;
    end;

    if DsItem = 1 then
    begin
      ListFilter1.Add(StringList_D);
      ListFilter1.Add(StringList_D1);
      ListFilter1.Add(StringList_D2);
      ListFilter1.Add(StringList_D3);
    end;

    if DsItem = 2 then
    begin
      ListFilter2.Add(StringList_D);
      ListFilter2.Add(StringList_D1);
      ListFilter2.Add(StringList_D2);
      ListFilter2.Add(StringList_D3);
    end;

    if DsItem = 3 then
    begin
      ListFilter3.Add(StringList_D);
      ListFilter3.Add(StringList_D1);
      ListFilter3.Add(StringList_D2);
      ListFilter3.Add(StringList_D3);
    end;

  end;

  if ListFilter1.Count > 0 then
  begin
    for i := 0 to (ListFilter1.Count div 4) - 1 do
    begin
      StringList_D := TStringList(ListFilter1.Items[4 * i + 0]);
      StringList_D1 := TStringList(ListFilter1.Items[4 * i + 1]);
      StringList_D2 := TStringList(ListFilter1.Items[4 * i + 2]);
      StringList_D3 := TStringList(ListFilter1.Items[4 * i + 3]);
      ListFilterAll.Add(StringList_D);
      ListFilterAll.Add(StringList_D1);
      ListFilterAll.Add(StringList_D2);
      ListFilterAll.Add(StringList_D3);
    end;
  end
  else
  begin
    if ListFilter2.Count > 0 then
    begin
      for i := 0 to (ListFilter2.Count div 4) - 1 do
      begin
        StringList_D := TStringList(ListFilter2.Items[4 * i + 0]);
        StringList_D1 := TStringList(ListFilter2.Items[4 * i + 1]);
        StringList_D2 := TStringList(ListFilter2.Items[4 * i + 2]);
        StringList_D3 := TStringList(ListFilter2.Items[4 * i + 3]);
        ListFilterAll.Add(StringList_D);
        ListFilterAll.Add(StringList_D1);
        ListFilterAll.Add(StringList_D2);
        ListFilterAll.Add(StringList_D3);
      end;
    end
    else
    begin
      if ListFilter3.Count > 0 then
      begin
        for i := 0 to (ListFilter3.Count div 4) - 1 do
        begin
          StringList_D := TStringList(ListFilter3.Items[4 * i + 0]);
          StringList_D1 := TStringList(ListFilter3.Items[4 * i + 1]);
          StringList_D2 := TStringList(ListFilter3.Items[4 * i + 2]);
          StringList_D3 := TStringList(ListFilter3.Items[4 * i + 3]);
          ListFilterAll.Add(StringList_D);
          ListFilterAll.Add(StringList_D1);
          ListFilterAll.Add(StringList_D2);
          ListFilterAll.Add(StringList_D3);
        end;
      end;
    end;
  end;

  // 排序
  DCountMin := 9999;
  DCountMinIndex := 0;
  DCountMinIndex1 := -1;
  for i := 0 to (ListFilterAll.Count div 4) - 1 do
  begin
    StringList_S := TStringList(ListFilterAll.Items[4 * i + 0]);
    StringList_D1 := TStringList(ListFilterAll.Items[4 * i + 1]);
    StringList_D2 := TStringList(ListFilterAll.Items[4 * i + 2]);
    StringList_D3 := TStringList(ListFilterAll.Items[4 * i + 3]);

    DCount := StrToInt(StringList_S[13]);
    if DCount < DCountMin then
    begin
      if DCountMinIndex = 0 then
      begin
        DCountMin := DCount;
        DCountMinIndex := i;
      end
      else
      begin
        DCountMinIndex1 := DCountMinIndex;
        DCountMin := DCount;
        DCountMinIndex := i;
      end;

    end;

  end;

  for i := 0 to (ListFilterAll.Count div 4) - 1 do
  begin
    if i = DCountMinIndex then
    begin
      StringList_S := TStringList(ListFilterAll.Items[4 * i + 0]);
      StringList_D1 := TStringList(ListFilterAll.Items[4 * i + 1]);
      StringList_D2 := TStringList(ListFilterAll.Items[4 * i + 2]);
      StringList_D3 := TStringList(ListFilterAll.Items[4 * i + 3]);
      ListFormat.Add(StringList_S);
      ListFormat.Add(StringList_D1);
      ListFormat.Add(StringList_D2);
      ListFormat.Add(StringList_D3);
      break;
    end;
  end;

  for i := 0 to (ListFormat.Count div 4) - 1 do
  begin
    StringList_S := TStringList(ListFormat.Items[4 * i + 0]);
    StringList_D1 := TStringList(ListFormat.Items[4 * i + 1]);
    StringList_D2 := TStringList(ListFormat.Items[4 * i + 2]);
    StringList_D3 := TStringList(ListFormat.Items[4 * i + 3]);
    ListFormatShow.Add(StringList_S);
    setlength(ItemIndex, 3);
    ItemIndex[0] := TOptimizationHelper.GetCachedIntValue(StringList_D1[14]);
    ItemIndex[1] := TOptimizationHelper.GetCachedIntValue(StringList_D2[14]);
    ItemIndex[2] := TOptimizationHelper.GetCachedIntValue(StringList_D3[14]);
    BubbleSort(ItemIndex);

    setlength(UsedIndex, 3);
    UsedIndex[0] := -1;
    UsedIndex[1] := -1;
    UsedIndex[2] := -1;

    for o := 0 to 2 do
    begin
      if (StrToInt(StringList_D1[14]) = ItemIndex[o]) and (UsedIndex[0] = -1)
      then
      begin
        ListFormatShow.Add(StringList_D1);
        UsedIndex[0] := 1;
      end
      else if (StrToInt(StringList_D2[14]) = ItemIndex[o]) and
        (UsedIndex[1] = -1) then
      begin
        ListFormatShow.Add(StringList_D2);
        UsedIndex[1] := 2;
      end
      else if (StrToInt(StringList_D3[14]) = ItemIndex[o]) and
        (UsedIndex[2] = -1) then
      begin
        ListFormatShow.Add(StringList_D3);
        UsedIndex[2] := 3;
      end
    end;
  end;

  ReturnList := TList.Create;
  for i := 0 to (ListFormatShow.Count div 4) - 1 do
  begin
    if i = 0 then
    begin
      if FlagType = 1 then
      begin
        ReturnList.Add(TStringList(ListFormatShow.Items[4 * i + 1]));
        ReturnList.Add(TStringList(ListFormatShow.Items[4 * i + 2]));
        ReturnList.Add(TStringList(ListFormatShow.Items[4 * i + 3]));
      end
      else
      begin
        ReturnList.Add(TStringList(ListFormatShow.Items[4 * i + 0]));
        ReturnList.Add(TStringList(ListFormatShow.Items[4 * i + 1]));
        ReturnList.Add(TStringList(ListFormatShow.Items[4 * i + 2]));
        ReturnList.Add(TStringList(ListFormatShow.Items[4 * i + 3]));
      end;
    end;
  end;

  result := ReturnList;

end;

procedure TThreadPdCal.FillOnceTen(ListIn: TList);
var
  ListFilter1, ListFilter2, ListFilter3, ListFilter4, ListFilter5, ListFilter6,
    ListFilter7, ListFilter8, ListFilter9, ListFilter10, ListFilterAll: TList;
  js: Integer;
  DsMin: Integer;
  DsItem: Integer;
  q: Integer;
  AveFlag: Boolean;
  i: Integer;
  StringList_S, StringList_D, StringList_D1, StringList_D2, StringList_D3,
    StringList_D4, StringList_D5, StringList_D6, StringList_D7, StringList_D8,
    StringList_D9, StringList_D10: TStringList;
  DCountMin, DCountMinIndex, DCountMinIndex1: Integer;
  CalDCountSum, CalDcount1, CalDCount2, CalDCount3, CalDCount4, CalDCount5,
    CalDCount6, CalDCount7, CalDCount8, CalDCount9, CalDCount10: Integer;
  DCount: Integer;
  List, ListFormat, ListFormatShow: TList;
  ItemIndex: Array of Integer;
  ItemCount: Integer;
  UsedIndex: Array of Integer;
  o: Integer;
begin
  ListFormat := TList.Create;

  ListFormatShow := TList.Create;

  DsMin := 0;
  ListFilter1 := TList.Create;
  ListFilter2 := TList.Create;
  ListFilter3 := TList.Create;
  ListFilter4 := TList.Create;
  ListFilter5 := TList.Create;
  ListFilter6 := TList.Create;
  ListFilter7 := TList.Create;
  ListFilter8 := TList.Create;
  ListFilter9 := TList.Create;
  ListFilter10 := TList.Create;
  ListFilterAll := TList.Create;
  for i := 0 to (ListIn.Count div 11) - 1 do
  begin
    DsItem := 0;
    StringList_D := TStringList(ListIn.Items[11 * i + 0]);
    StringList_D1 := TStringList(ListIn.Items[11 * i + 1]);
    StringList_D2 := TStringList(ListIn.Items[11 * i + 2]);
    StringList_D3 := TStringList(ListIn.Items[11 * i + 3]);
    StringList_D4 := TStringList(ListIn.Items[11 * i + 4]);
    StringList_D5 := TStringList(ListIn.Items[11 * i + 5]);
    StringList_D6 := TStringList(ListIn.Items[11 * i + 6]);
    StringList_D7 := TStringList(ListIn.Items[11 * i + 7]);
    StringList_D8 := TStringList(ListIn.Items[11 * i + 8]);
    StringList_D9 := TStringList(ListIn.Items[11 * i + 9]);
    StringList_D10 := TStringList(ListIn.Items[11 * i + 10]);

    if StringList_D1.Count > 0 then
    begin
      if (StrToInt(StringList_D1[0]) + StrToInt(StringList_D1[1]) +
        StrToInt(StringList_D1[2]) + StrToInt(StringList_D1[3]) +
        StrToInt(StringList_D1[4]) + StrToInt(StringList_D1[5]) +
        StrToInt(StringList_D1[6]) + StrToInt(StringList_D1[7]) +
        StrToInt(StringList_D1[8]) + StrToInt(StringList_D1[9]) +
        StrToInt(StringList_D1[10]) + StrToInt(StringList_D1[11]) +
        StrToInt(StringList_D1[12])) > 0 then
      begin
        DsItem := DsItem + 1;
      end;
    end;

    // 优化：使用批量求和替代重复的StrToInt调用
    if StringList_D2.Count > 0 then
    begin
      if TOptimizationHelper.CalculateStringListSum(StringList_D2, 0, CMPZSUM) > 0 then
      begin
        DsItem := DsItem + 1;
      end;
    end;

    if StringList_D3.Count > 0 then
    begin
      if TOptimizationHelper.CalculateStringListSum(StringList_D3, 0, CMPZSUM) > 0 then
      begin
        DsItem := DsItem + 1;
      end;
    end;

    if StringList_D4.Count > 0 then
    begin
      if TOptimizationHelper.CalculateStringListSum(StringList_D4, 0, CMPZSUM) > 0 then
      begin
        DsItem := DsItem + 1;
      end;
    end;

    if StringList_D5.Count > 0 then
    begin
      if TOptimizationHelper.CalculateStringListSum(StringList_D5, 0, CMPZSUM) > 0 then
      begin
        DsItem := DsItem + 1;
      end;
    end;

    // 优化：使用批量求和替代重复的StrToInt调用
    if StringList_D6.Count > 0 then
    begin
      if TOptimizationHelper.CalculateStringListSum(StringList_D6, 0, CMPZSUM) > 0 then
      begin
        DsItem := DsItem + 1;
      end;
    end;

    if StringList_D7.Count > 0 then
    begin
      if TOptimizationHelper.CalculateStringListSum(StringList_D7, 0, CMPZSUM) > 0 then
      begin
        DsItem := DsItem + 1;
      end;
    end;

    if StringList_D8.Count > 0 then
    begin
      if TOptimizationHelper.CalculateStringListSum(StringList_D8, 0, CMPZSUM) > 0 then
      begin
        DsItem := DsItem + 1;
      end;
    end;

    if StringList_D9.Count > 0 then
    begin
      if TOptimizationHelper.CalculateStringListSum(StringList_D9, 0, CMPZSUM) > 0 then
      begin
        DsItem := DsItem + 1;
      end;
    end;

    if StringList_D10.Count > 0 then
    begin
      if TOptimizationHelper.CalculateStringListSum(StringList_D10, 0, CMPZSUM) > 0 then
      begin
        DsItem := DsItem + 1;
      end;
    end;

    if DsMin < DsItem then
    begin
      DsMin := DsItem;
    end;

    if DsItem = 1 then
    begin
      ListFilter1.Add(StringList_D);
      ListFilter1.Add(StringList_D1);
      ListFilter1.Add(StringList_D2);
      ListFilter1.Add(StringList_D3);
      ListFilter1.Add(StringList_D4);
      ListFilter1.Add(StringList_D5);
      ListFilter1.Add(StringList_D6);
      ListFilter1.Add(StringList_D7);
      ListFilter1.Add(StringList_D8);
      ListFilter1.Add(StringList_D9);
      ListFilter1.Add(StringList_D10);
    end;

    if DsItem = 2 then
    begin
      ListFilter2.Add(StringList_D);
      ListFilter2.Add(StringList_D1);
      ListFilter2.Add(StringList_D2);
      ListFilter2.Add(StringList_D3);
      ListFilter2.Add(StringList_D4);
      ListFilter2.Add(StringList_D5);
      ListFilter2.Add(StringList_D6);
      ListFilter2.Add(StringList_D7);
      ListFilter2.Add(StringList_D8);
      ListFilter2.Add(StringList_D9);
      ListFilter2.Add(StringList_D10);
    end;

    if DsItem = 3 then
    begin
      ListFilter3.Add(StringList_D);
      ListFilter3.Add(StringList_D1);
      ListFilter3.Add(StringList_D2);
      ListFilter3.Add(StringList_D3);
      ListFilter3.Add(StringList_D4);
      ListFilter3.Add(StringList_D5);
      ListFilter3.Add(StringList_D6);
      ListFilter3.Add(StringList_D7);
      ListFilter3.Add(StringList_D8);
      ListFilter3.Add(StringList_D9);
      ListFilter3.Add(StringList_D10);
    end;

    if DsItem = 4 then
    begin
      ListFilter4.Add(StringList_D);
      ListFilter4.Add(StringList_D1);
      ListFilter4.Add(StringList_D2);
      ListFilter4.Add(StringList_D3);
      ListFilter4.Add(StringList_D4);
      ListFilter4.Add(StringList_D5);
      ListFilter4.Add(StringList_D6);
      ListFilter4.Add(StringList_D7);
      ListFilter4.Add(StringList_D8);
      ListFilter4.Add(StringList_D9);
      ListFilter4.Add(StringList_D10);
    end;

    if DsItem = 5 then
    begin
      ListFilter5.Add(StringList_D);
      ListFilter5.Add(StringList_D1);
      ListFilter5.Add(StringList_D2);
      ListFilter5.Add(StringList_D3);
      ListFilter5.Add(StringList_D4);
      ListFilter5.Add(StringList_D5);
      ListFilter5.Add(StringList_D6);
      ListFilter5.Add(StringList_D7);
      ListFilter5.Add(StringList_D8);
      ListFilter5.Add(StringList_D9);
      ListFilter5.Add(StringList_D10);
    end;

    if DsItem = 6 then
    begin
      ListFilter6.Add(StringList_D);
      ListFilter6.Add(StringList_D1);
      ListFilter6.Add(StringList_D2);
      ListFilter6.Add(StringList_D3);
      ListFilter6.Add(StringList_D4);
      ListFilter6.Add(StringList_D5);
      ListFilter6.Add(StringList_D6);
      ListFilter6.Add(StringList_D7);
      ListFilter6.Add(StringList_D8);
      ListFilter6.Add(StringList_D9);
      ListFilter6.Add(StringList_D10);
    end;

    if DsItem = 7 then
    begin
      ListFilter7.Add(StringList_D);
      ListFilter7.Add(StringList_D1);
      ListFilter7.Add(StringList_D2);
      ListFilter7.Add(StringList_D3);
      ListFilter7.Add(StringList_D4);
      ListFilter7.Add(StringList_D5);
      ListFilter7.Add(StringList_D6);
      ListFilter7.Add(StringList_D7);
      ListFilter7.Add(StringList_D8);
      ListFilter7.Add(StringList_D9);
      ListFilter7.Add(StringList_D10);
    end;

    if DsItem = 8 then
    begin
      ListFilter8.Add(StringList_D);
      ListFilter8.Add(StringList_D1);
      ListFilter8.Add(StringList_D2);
      ListFilter8.Add(StringList_D3);
      ListFilter8.Add(StringList_D4);
      ListFilter8.Add(StringList_D5);
      ListFilter8.Add(StringList_D6);
      ListFilter8.Add(StringList_D7);
      ListFilter8.Add(StringList_D8);
      ListFilter8.Add(StringList_D9);
      ListFilter8.Add(StringList_D10);
    end;

    if DsItem = 9 then
    begin
      ListFilter9.Add(StringList_D);
      ListFilter9.Add(StringList_D1);
      ListFilter9.Add(StringList_D2);
      ListFilter9.Add(StringList_D3);
      ListFilter9.Add(StringList_D4);
      ListFilter9.Add(StringList_D5);
      ListFilter9.Add(StringList_D6);
      ListFilter9.Add(StringList_D7);
      ListFilter9.Add(StringList_D8);
      ListFilter9.Add(StringList_D9);
      ListFilter9.Add(StringList_D10);
    end;

    if DsItem = 10 then
    begin
      ListFilter10.Add(StringList_D);
      ListFilter10.Add(StringList_D1);
      ListFilter10.Add(StringList_D2);
      ListFilter10.Add(StringList_D3);
      ListFilter10.Add(StringList_D4);
      ListFilter10.Add(StringList_D5);
      ListFilter10.Add(StringList_D6);
      ListFilter10.Add(StringList_D7);
      ListFilter10.Add(StringList_D8);
      ListFilter10.Add(StringList_D9);
      ListFilter10.Add(StringList_D10);
    end;

  end;

  if ListFilter1.Count > 0 then
  begin
    for i := 0 to (ListFilter1.Count div 11) - 1 do
    begin
      StringList_D := TStringList(ListFilter1.Items[11 * i + 0]);
      StringList_D1 := TStringList(ListFilter1.Items[11 * i + 1]);
      StringList_D2 := TStringList(ListFilter1.Items[11 * i + 2]);
      StringList_D3 := TStringList(ListFilter1.Items[11 * i + 3]);
      StringList_D4 := TStringList(ListFilter1.Items[11 * i + 4]);
      StringList_D5 := TStringList(ListFilter1.Items[11 * i + 5]);
      StringList_D6 := TStringList(ListFilter1.Items[11 * i + 6]);
      StringList_D7 := TStringList(ListFilter1.Items[11 * i + 7]);
      StringList_D8 := TStringList(ListFilter1.Items[11 * i + 8]);
      StringList_D9 := TStringList(ListFilter1.Items[11 * i + 9]);
      StringList_D10 := TStringList(ListFilter1.Items[11 * i + 10]);
      ListFilterAll.Add(StringList_D);
      ListFilterAll.Add(StringList_D1);
      ListFilterAll.Add(StringList_D2);
      ListFilterAll.Add(StringList_D3);
      ListFilterAll.Add(StringList_D4);
      ListFilterAll.Add(StringList_D5);
      ListFilterAll.Add(StringList_D6);
      ListFilterAll.Add(StringList_D7);
      ListFilterAll.Add(StringList_D8);
      ListFilterAll.Add(StringList_D9);
      ListFilterAll.Add(StringList_D10);
    end;
  end
  else
  begin
    if ListFilter2.Count > 0 then
    begin
      for i := 0 to (ListFilter2.Count div 11) - 1 do
      begin
        StringList_D := TStringList(ListFilter2.Items[11 * i + 0]);
        StringList_D1 := TStringList(ListFilter2.Items[11 * i + 1]);
        StringList_D2 := TStringList(ListFilter2.Items[11 * i + 2]);
        StringList_D3 := TStringList(ListFilter2.Items[11 * i + 3]);
        StringList_D4 := TStringList(ListFilter2.Items[11 * i + 4]);
        StringList_D5 := TStringList(ListFilter2.Items[11 * i + 5]);
        StringList_D6 := TStringList(ListFilter2.Items[11 * i + 6]);
        StringList_D7 := TStringList(ListFilter2.Items[11 * i + 7]);
        StringList_D8 := TStringList(ListFilter2.Items[11 * i + 8]);
        StringList_D9 := TStringList(ListFilter2.Items[11 * i + 9]);
        StringList_D10 := TStringList(ListFilter2.Items[11 * i + 10]);
        ListFilterAll.Add(StringList_D);
        ListFilterAll.Add(StringList_D1);
        ListFilterAll.Add(StringList_D2);
        ListFilterAll.Add(StringList_D3);
        ListFilterAll.Add(StringList_D4);
        ListFilterAll.Add(StringList_D5);
        ListFilterAll.Add(StringList_D6);
        ListFilterAll.Add(StringList_D7);
        ListFilterAll.Add(StringList_D8);
        ListFilterAll.Add(StringList_D9);
        ListFilterAll.Add(StringList_D10);
      end;
    end
    else
    begin
      if ListFilter3.Count > 0 then
      begin
        for i := 0 to (ListFilter3.Count div 11) - 1 do
        begin
          StringList_D := TStringList(ListFilter3.Items[11 * i + 0]);
          StringList_D1 := TStringList(ListFilter3.Items[11 * i + 1]);
          StringList_D2 := TStringList(ListFilter3.Items[11 * i + 2]);
          StringList_D3 := TStringList(ListFilter3.Items[11 * i + 3]);
          StringList_D4 := TStringList(ListFilter3.Items[11 * i + 4]);
          StringList_D5 := TStringList(ListFilter3.Items[11 * i + 5]);
          StringList_D6 := TStringList(ListFilter3.Items[11 * i + 6]);
          StringList_D7 := TStringList(ListFilter3.Items[11 * i + 7]);
          StringList_D8 := TStringList(ListFilter3.Items[11 * i + 8]);
          StringList_D9 := TStringList(ListFilter3.Items[11 * i + 9]);
          StringList_D10 := TStringList(ListFilter3.Items[11 * i + 10]);
          ListFilterAll.Add(StringList_D);
          ListFilterAll.Add(StringList_D1);
          ListFilterAll.Add(StringList_D2);
          ListFilterAll.Add(StringList_D3);
          ListFilterAll.Add(StringList_D4);
          ListFilterAll.Add(StringList_D5);
          ListFilterAll.Add(StringList_D6);
          ListFilterAll.Add(StringList_D7);
          ListFilterAll.Add(StringList_D8);
          ListFilterAll.Add(StringList_D9);
          ListFilterAll.Add(StringList_D10);
        end;
      end
      else
      begin
        if ListFilter4.Count > 0 then
        begin
          for i := 0 to (ListFilter4.Count div 11) - 1 do
          begin
            StringList_D := TStringList(ListFilter4.Items[11 * i + 0]);
            StringList_D1 := TStringList(ListFilter4.Items[11 * i + 1]);
            StringList_D2 := TStringList(ListFilter4.Items[11 * i + 2]);
            StringList_D3 := TStringList(ListFilter4.Items[11 * i + 3]);
            StringList_D4 := TStringList(ListFilter4.Items[11 * i + 4]);
            StringList_D5 := TStringList(ListFilter4.Items[11 * i + 5]);
            StringList_D6 := TStringList(ListFilter4.Items[11 * i + 6]);
            StringList_D7 := TStringList(ListFilter4.Items[11 * i + 7]);
            StringList_D8 := TStringList(ListFilter4.Items[11 * i + 8]);
            StringList_D9 := TStringList(ListFilter4.Items[11 * i + 9]);
            StringList_D10 := TStringList(ListFilter4.Items[11 * i + 10]);
            ListFilterAll.Add(StringList_D);
            ListFilterAll.Add(StringList_D1);
            ListFilterAll.Add(StringList_D2);
            ListFilterAll.Add(StringList_D3);
            ListFilterAll.Add(StringList_D4);
            ListFilterAll.Add(StringList_D5);
            ListFilterAll.Add(StringList_D6);
            ListFilterAll.Add(StringList_D7);
            ListFilterAll.Add(StringList_D8);
            ListFilterAll.Add(StringList_D9);
            ListFilterAll.Add(StringList_D10);
          end;
        end
        else
        begin
          if ListFilter5.Count > 0 then
          begin
            for i := 0 to (ListFilter5.Count div 11) - 1 do
            begin
              StringList_D := TStringList(ListFilter5.Items[11 * i + 0]);
              StringList_D1 := TStringList(ListFilter5.Items[11 * i + 1]);
              StringList_D2 := TStringList(ListFilter5.Items[11 * i + 2]);
              StringList_D3 := TStringList(ListFilter5.Items[11 * i + 3]);
              StringList_D4 := TStringList(ListFilter5.Items[11 * i + 4]);
              StringList_D5 := TStringList(ListFilter5.Items[11 * i + 5]);
              StringList_D6 := TStringList(ListFilter5.Items[11 * i + 6]);
              StringList_D7 := TStringList(ListFilter5.Items[11 * i + 7]);
              StringList_D8 := TStringList(ListFilter5.Items[11 * i + 8]);
              StringList_D9 := TStringList(ListFilter5.Items[11 * i + 9]);
              StringList_D10 := TStringList(ListFilter5.Items[11 * i + 10]);
              ListFilterAll.Add(StringList_D);
              ListFilterAll.Add(StringList_D1);
              ListFilterAll.Add(StringList_D2);
              ListFilterAll.Add(StringList_D3);
              ListFilterAll.Add(StringList_D4);
              ListFilterAll.Add(StringList_D5);
              ListFilterAll.Add(StringList_D6);
              ListFilterAll.Add(StringList_D7);
              ListFilterAll.Add(StringList_D8);
              ListFilterAll.Add(StringList_D9);
              ListFilterAll.Add(StringList_D10);
            end;
          end
          else
          begin
            if ListFilter6.Count > 0 then
            begin
              for i := 0 to (ListFilter6.Count div 11) - 1 do
              begin
                StringList_D := TStringList(ListFilter6.Items[11 * i + 0]);
                StringList_D1 := TStringList(ListFilter6.Items[11 * i + 1]);
                StringList_D2 := TStringList(ListFilter6.Items[11 * i + 2]);
                StringList_D3 := TStringList(ListFilter6.Items[11 * i + 3]);
                StringList_D4 := TStringList(ListFilter6.Items[11 * i + 4]);
                StringList_D5 := TStringList(ListFilter6.Items[11 * i + 5]);
                StringList_D6 := TStringList(ListFilter6.Items[11 * i + 6]);
                StringList_D7 := TStringList(ListFilter6.Items[11 * i + 7]);
                StringList_D8 := TStringList(ListFilter6.Items[11 * i + 8]);
                StringList_D9 := TStringList(ListFilter6.Items[11 * i + 9]);
                StringList_D10 := TStringList(ListFilter6.Items[11 * i + 10]);
                ListFilterAll.Add(StringList_D);
                ListFilterAll.Add(StringList_D1);
                ListFilterAll.Add(StringList_D2);
                ListFilterAll.Add(StringList_D3);
                ListFilterAll.Add(StringList_D4);
                ListFilterAll.Add(StringList_D5);
                ListFilterAll.Add(StringList_D6);
                ListFilterAll.Add(StringList_D7);
                ListFilterAll.Add(StringList_D8);
                ListFilterAll.Add(StringList_D9);
                ListFilterAll.Add(StringList_D10);
              end;
            end
            else
            begin
              if ListFilter7.Count > 0 then
              begin
                for i := 0 to (ListFilter7.Count div 11) - 1 do
                begin
                  StringList_D := TStringList(ListFilter7.Items[11 * i + 0]);
                  StringList_D1 := TStringList(ListFilter7.Items[11 * i + 1]);
                  StringList_D2 := TStringList(ListFilter7.Items[11 * i + 2]);
                  StringList_D3 := TStringList(ListFilter7.Items[11 * i + 3]);
                  StringList_D4 := TStringList(ListFilter7.Items[11 * i + 4]);
                  StringList_D5 := TStringList(ListFilter7.Items[11 * i + 5]);
                  StringList_D6 := TStringList(ListFilter7.Items[11 * i + 6]);
                  StringList_D7 := TStringList(ListFilter7.Items[11 * i + 7]);
                  StringList_D8 := TStringList(ListFilter7.Items[11 * i + 8]);
                  StringList_D9 := TStringList(ListFilter7.Items[11 * i + 9]);
                  StringList_D10 := TStringList(ListFilter7.Items[11 * i + 10]);
                  ListFilterAll.Add(StringList_D);
                  ListFilterAll.Add(StringList_D1);
                  ListFilterAll.Add(StringList_D2);
                  ListFilterAll.Add(StringList_D3);
                  ListFilterAll.Add(StringList_D4);
                  ListFilterAll.Add(StringList_D5);
                  ListFilterAll.Add(StringList_D6);
                  ListFilterAll.Add(StringList_D7);
                  ListFilterAll.Add(StringList_D8);
                  ListFilterAll.Add(StringList_D9);
                  ListFilterAll.Add(StringList_D10);
                end;
              end
              else
              begin
                if ListFilter8.Count > 0 then
                begin
                  for i := 0 to (ListFilter8.Count div 11) - 1 do
                  begin
                    StringList_D := TStringList(ListFilter8.Items[11 * i + 0]);
                    StringList_D1 := TStringList(ListFilter8.Items[11 * i + 1]);
                    StringList_D2 := TStringList(ListFilter8.Items[11 * i + 2]);
                    StringList_D3 := TStringList(ListFilter8.Items[11 * i + 3]);
                    StringList_D4 := TStringList(ListFilter8.Items[11 * i + 4]);
                    StringList_D5 := TStringList(ListFilter8.Items[11 * i + 5]);
                    StringList_D6 := TStringList(ListFilter8.Items[11 * i + 6]);
                    StringList_D7 := TStringList(ListFilter8.Items[11 * i + 7]);
                    StringList_D8 := TStringList(ListFilter8.Items[11 * i + 8]);
                    StringList_D9 := TStringList(ListFilter8.Items[11 * i + 9]);
                    StringList_D10 :=
                      TStringList(ListFilter8.Items[11 * i + 10]);
                    ListFilterAll.Add(StringList_D);
                    ListFilterAll.Add(StringList_D1);
                    ListFilterAll.Add(StringList_D2);
                    ListFilterAll.Add(StringList_D3);
                    ListFilterAll.Add(StringList_D4);
                    ListFilterAll.Add(StringList_D5);
                    ListFilterAll.Add(StringList_D6);
                    ListFilterAll.Add(StringList_D7);
                    ListFilterAll.Add(StringList_D8);
                    ListFilterAll.Add(StringList_D9);
                    ListFilterAll.Add(StringList_D10);
                  end;
                end
                else
                begin
                  if ListFilter9.Count > 0 then
                  begin
                    for i := 0 to (ListFilter9.Count div 11) - 1 do
                    begin
                      StringList_D :=
                        TStringList(ListFilter9.Items[11 * i + 0]);
                      StringList_D1 :=
                        TStringList(ListFilter9.Items[11 * i + 1]);
                      StringList_D2 :=
                        TStringList(ListFilter9.Items[11 * i + 2]);
                      StringList_D3 :=
                        TStringList(ListFilter9.Items[11 * i + 3]);
                      StringList_D4 :=
                        TStringList(ListFilter9.Items[11 * i + 4]);
                      StringList_D5 :=
                        TStringList(ListFilter9.Items[11 * i + 5]);
                      StringList_D6 :=
                        TStringList(ListFilter9.Items[11 * i + 6]);
                      StringList_D7 :=
                        TStringList(ListFilter9.Items[11 * i + 7]);
                      StringList_D8 :=
                        TStringList(ListFilter9.Items[11 * i + 8]);
                      StringList_D9 :=
                        TStringList(ListFilter9.Items[11 * i + 9]);
                      StringList_D10 :=
                        TStringList(ListFilter9.Items[11 * i + 10]);
                      ListFilterAll.Add(StringList_D);
                      ListFilterAll.Add(StringList_D1);
                      ListFilterAll.Add(StringList_D2);
                      ListFilterAll.Add(StringList_D3);
                      ListFilterAll.Add(StringList_D4);
                      ListFilterAll.Add(StringList_D5);
                      ListFilterAll.Add(StringList_D6);
                      ListFilterAll.Add(StringList_D7);
                      ListFilterAll.Add(StringList_D8);
                      ListFilterAll.Add(StringList_D9);
                      ListFilterAll.Add(StringList_D10);
                    end;
                  end
                  else
                  begin
                    if ListFilter10.Count > 0 then
                    begin
                      for i := 0 to (ListFilter10.Count div 11) - 1 do
                      begin
                        StringList_D :=
                          TStringList(ListFilter10.Items[11 * i + 0]);
                        StringList_D1 :=
                          TStringList(ListFilter10.Items[11 * i + 1]);
                        StringList_D2 :=
                          TStringList(ListFilter10.Items[11 * i + 2]);
                        StringList_D3 :=
                          TStringList(ListFilter10.Items[11 * i + 3]);
                        StringList_D4 :=
                          TStringList(ListFilter10.Items[11 * i + 4]);
                        StringList_D5 :=
                          TStringList(ListFilter10.Items[11 * i + 5]);
                        StringList_D6 :=
                          TStringList(ListFilter10.Items[11 * i + 6]);
                        StringList_D7 :=
                          TStringList(ListFilter10.Items[11 * i + 7]);
                        StringList_D8 :=
                          TStringList(ListFilter10.Items[11 * i + 8]);
                        StringList_D9 :=
                          TStringList(ListFilter10.Items[11 * i + 9]);
                        StringList_D10 :=
                          TStringList(ListFilter10.Items[11 * i + 10]);
                        ListFilterAll.Add(StringList_D);
                        ListFilterAll.Add(StringList_D1);
                        ListFilterAll.Add(StringList_D2);
                        ListFilterAll.Add(StringList_D3);
                        ListFilterAll.Add(StringList_D4);
                        ListFilterAll.Add(StringList_D5);
                        ListFilterAll.Add(StringList_D6);
                        ListFilterAll.Add(StringList_D7);
                        ListFilterAll.Add(StringList_D8);
                        ListFilterAll.Add(StringList_D9);
                        ListFilterAll.Add(StringList_D10);
                      end;
                    end;
                  end;
                end;
              end;
            end;
          end;
        end;
      end;
    end;
  end;

  // 排序
  DCountMin := 9999;
  DCountMinIndex := 0;
  DCountMinIndex1 := -1;
  for i := 0 to (ListFilterAll.Count div 11) - 1 do
  begin
    StringList_S := TStringList(ListFilterAll.Items[11 * i + 0]);
    StringList_D1 := TStringList(ListFilterAll.Items[11 * i + 1]);
    StringList_D2 := TStringList(ListFilterAll.Items[11 * i + 2]);
    StringList_D3 := TStringList(ListFilterAll.Items[11 * i + 3]);
    StringList_D4 := TStringList(ListFilterAll.Items[11 * i + 4]);
    StringList_D5 := TStringList(ListFilterAll.Items[11 * i + 5]);
    StringList_D6 := TStringList(ListFilterAll.Items[11 * i + 6]);
    StringList_D7 := TStringList(ListFilterAll.Items[11 * i + 7]);
    StringList_D8 := TStringList(ListFilterAll.Items[11 * i + 8]);
    StringList_D9 := TStringList(ListFilterAll.Items[11 * i + 9]);
    StringList_D10 := TStringList(ListFilterAll.Items[11 * i + 10]);

    DCount := StrToInt(StringList_S[13]);
    if DCount < DCountMin then
    begin
      if DCountMinIndex = 0 then
      begin
        DCountMin := DCount;
        DCountMinIndex := i;
      end
      else
      begin
        DCountMinIndex1 := DCountMinIndex;
        DCountMin := DCount;
        DCountMinIndex := i;
      end;

    end;

  end;

  for i := 0 to (ListFilterAll.Count div 11) - 1 do
  begin
    if i = DCountMinIndex then
    begin
      StringList_S := TStringList(ListFilterAll.Items[11 * i + 0]);
      StringList_D1 := TStringList(ListFilterAll.Items[11 * i + 1]);
      StringList_D2 := TStringList(ListFilterAll.Items[11 * i + 2]);
      StringList_D3 := TStringList(ListFilterAll.Items[11 * i + 3]);
      StringList_D4 := TStringList(ListFilterAll.Items[11 * i + 4]);
      StringList_D5 := TStringList(ListFilterAll.Items[11 * i + 5]);
      StringList_D6 := TStringList(ListFilterAll.Items[11 * i + 6]);
      StringList_D7 := TStringList(ListFilterAll.Items[11 * i + 7]);
      StringList_D8 := TStringList(ListFilterAll.Items[11 * i + 8]);
      StringList_D9 := TStringList(ListFilterAll.Items[11 * i + 9]);
      StringList_D10 := TStringList(ListFilterAll.Items[11 * i + 10]);

      ListFormat.Add(StringList_S);
      ListFormat.Add(StringList_D1);
      ListFormat.Add(StringList_D2);
      ListFormat.Add(StringList_D3);
      ListFormat.Add(StringList_D4);
      ListFormat.Add(StringList_D5);
      ListFormat.Add(StringList_D6);
      ListFormat.Add(StringList_D7);
      ListFormat.Add(StringList_D8);
      ListFormat.Add(StringList_D9);
      ListFormat.Add(StringList_D10);
      break;
    end;
  end;

  for i := 0 to (ListFormat.Count div 11) - 1 do
  begin
    StringList_S := TStringList(ListFormat.Items[11 * i + 0]);
    StringList_D1 := TStringList(ListFormat.Items[11 * i + 1]);
    StringList_D2 := TStringList(ListFormat.Items[11 * i + 2]);
    StringList_D3 := TStringList(ListFormat.Items[11 * i + 3]);
    StringList_D4 := TStringList(ListFormat.Items[11 * i + 4]);
    StringList_D5 := TStringList(ListFormat.Items[11 * i + 5]);
    StringList_D6 := TStringList(ListFormat.Items[11 * i + 6]);
    StringList_D7 := TStringList(ListFormat.Items[11 * i + 7]);
    StringList_D8 := TStringList(ListFormat.Items[11 * i + 8]);
    StringList_D9 := TStringList(ListFormat.Items[11 * i + 9]);
    StringList_D10 := TStringList(ListFormat.Items[11 * i + 10]);
    ListFormatShow.Add(StringList_S);
    setlength(ItemIndex, 10);

    ItemIndex[0] := TOptimizationHelper.GetCachedIntValue(StringList_D1[14]);
    ItemIndex[1] := TOptimizationHelper.GetCachedIntValue(StringList_D2[14]);
    ItemIndex[2] := TOptimizationHelper.GetCachedIntValue(StringList_D3[14]);
    ItemIndex[3] := TOptimizationHelper.GetCachedIntValue(StringList_D4[14]);
    ItemIndex[4] := TOptimizationHelper.GetCachedIntValue(StringList_D5[14]);
    ItemIndex[5] := TOptimizationHelper.GetCachedIntValue(StringList_D6[14]);
    ItemIndex[6] := TOptimizationHelper.GetCachedIntValue(StringList_D7[14]);
    ItemIndex[7] := TOptimizationHelper.GetCachedIntValue(StringList_D8[14]);
    ItemIndex[8] := TOptimizationHelper.GetCachedIntValue(StringList_D9[14]);
    ItemIndex[9] := TOptimizationHelper.GetCachedIntValue(StringList_D10[14]);

    BubbleSort(ItemIndex);

    setlength(UsedIndex, 10);
    UsedIndex[0] := -1;
    UsedIndex[1] := -1;
    UsedIndex[2] := -1;
    UsedIndex[3] := -1;
    UsedIndex[4] := -1;
    UsedIndex[5] := -1;
    UsedIndex[6] := -1;
    UsedIndex[7] := -1;
    UsedIndex[8] := -1;
    UsedIndex[9] := -1;
    for o := 0 to 9 do
    begin
      if (StrToInt(StringList_D1[14]) = ItemIndex[o]) and (UsedIndex[0] = -1)
      then
      begin
        ListFormatShow.Add(StringList_D1);
        UsedIndex[0] := 1;
      end
      else if (StrToInt(StringList_D2[14]) = ItemIndex[o]) and
        (UsedIndex[1] = -1) then
      begin
        ListFormatShow.Add(StringList_D2);
        UsedIndex[1] := 2;
      end
      else if (StrToInt(StringList_D3[14]) = ItemIndex[o]) and
        (UsedIndex[2] = -1) then
      begin
        ListFormatShow.Add(StringList_D3);
        UsedIndex[2] := 3;
      end
      else if (StrToInt(StringList_D4[14]) = ItemIndex[o]) and
        (UsedIndex[3] = -1) then
      begin
        ListFormatShow.Add(StringList_D4);
        UsedIndex[3] := 4;
      end
      else if (StrToInt(StringList_D5[14]) = ItemIndex[o]) and
        (UsedIndex[4] = -1) then
      begin
        ListFormatShow.Add(StringList_D5);
        UsedIndex[4] := 5;
      end
      else if (StrToInt(StringList_D6[14]) = ItemIndex[o]) and
        (UsedIndex[5] = -1) then
      begin
        ListFormatShow.Add(StringList_D6);
        UsedIndex[5] := 6;
      end
      else if (StrToInt(StringList_D7[14]) = ItemIndex[o]) and
        (UsedIndex[6] = -1) then
      begin
        ListFormatShow.Add(StringList_D7);
        UsedIndex[6] := 7;
      end
      else if (StrToInt(StringList_D8[14]) = ItemIndex[o]) and
        (UsedIndex[7] = -1) then
      begin
        ListFormatShow.Add(StringList_D8);
        UsedIndex[7] := 8;
      end
      else if (StrToInt(StringList_D9[14]) = ItemIndex[o]) and
        (UsedIndex[8] = -1) then
      begin
        ListFormatShow.Add(StringList_D9);
        UsedIndex[8] := 9;
      end
      else if (StrToInt(StringList_D10[14]) = ItemIndex[o]) and
        (UsedIndex[9] = -1) then
      begin
        ListFormatShow.Add(StringList_D10);
        UsedIndex[9] := 10;
      end;
    end;
  end;

  for i := 0 to (ListFormatShow.Count div 11) - 1 do
  begin
    if i = 0 then
    begin
      FillGrid2Ten(TStringList(ListFormatShow.Items[11 * i + 0]),
        TStringList(ListFormatShow.Items[11 * i + 1]),
        TStringList(ListFormatShow.Items[11 * i + 2]),
        TStringList(ListFormatShow.Items[11 * i + 3]),
        TStringList(ListFormatShow.Items[11 * i + 4]),
        TStringList(ListFormatShow.Items[11 * i + 5]),
        TStringList(ListFormatShow.Items[11 * i + 6]),
        TStringList(ListFormatShow.Items[11 * i + 7]),
        TStringList(ListFormatShow.Items[11 * i + 8]),
        TStringList(ListFormatShow.Items[11 * i + 9]),
        TStringList(ListFormatShow.Items[11 * i + 10]));
    end;

  end;

end;

procedure TThreadPdCal.FormatStrings;
var
  C: array of Integer;
  StringList: TStringList;
  i, j, k: Integer;
  XxDd: TXxDd;
  ManStringList: TStringList;
  WomenStringList: TStringList;
  ChildStringList: TStringList;
  Cm: string;
  flag: Boolean;
  CcMaterial: TCcMaterial;
  MaterialIdStr: string;
  SDataStringList_Kc: TStringList;
  SDataStringList_All: TStringList;
  SData1StringList: TStringList;
  MATERIALFZID: Integer;
  cmtypenum: Integer;
begin
  // ManStringList := TStringList.Create;
  // WomenStringList := TStringList.Create;
  // ChildStringList := TStringList.Create;
  //
  // // 9XL,10XL 加入
  // ManStringList.Add('XS');
  // ManStringList.Add('S');
  // ManStringList.Add('M');
  // ManStringList.Add('L');
  // ManStringList.Add('XL');
  // ManStringList.Add('2XL');
  // ManStringList.Add('3XL');
  // ManStringList.Add('4XL');
  // ManStringList.Add('5XL');
  // ManStringList.Add('7XL');
  //
  // // 26 28 加入
  // WomenStringList.Add('6');
  // WomenStringList.Add('8');
  // WomenStringList.Add('10');
  // WomenStringList.Add('12');
  // WomenStringList.Add('14');
  // WomenStringList.Add('16');
  // WomenStringList.Add('18');
  // WomenStringList.Add('20');
  // WomenStringList.Add('22');
  // WomenStringList.Add('24');
  //
  // // 18,20 未加入
  // ChildStringList.Add('0');
  // ChildStringList.Add('1');
  // ChildStringList.Add('2');
  // ChildStringList.Add('4');
  // ChildStringList.Add('6');
  // ChildStringList.Add('8');
  // ChildStringList.Add('10');
  // ChildStringList.Add('12');
  // ChildStringList.Add('14');
  // ChildStringList.Add('16');


  // 0 1 2	4	6	8	10	12	14  16
  // 6	8	10	12	14	16	18	20	22	24
  // XS	S	M	L	XL	2XL	3XL	4XL	5XL	7XL

  for k := 0 to 5 - 1 do
  begin
    MATERIALFZID := 0;
    // 计算库存进行存储
    SDataStringList_Kc := TStringList.Create;
    SDataStringList_All := TStringList.Create;
    SData1StringList := TStringList.Create;

    if k = 0 then
    begin
      StringList := Source_Data1StringList;
    end;

    if k = 1 then
    begin
      StringList := Source_Data2StringList;
    end;

    if k = 2 then
    begin
      StringList := Source_Data3StringList;
    end;

    if k = 3 then
    begin
      StringList := Source_Data4StringList;
    end;

    if k = 4 then
    begin
      StringList := Source_Data5StringList;
    end;

    for i := 0 to CMPZSUM - 1 do
    begin
      flag := false;
      for j := 0 to StringList.Count - 1 do
      begin
        XxDd := TXxDd(StringList.Objects[j]);
        if XxDd.CmNum = i + 1 then
        begin
          SDataStringList_Kc.Add(IntToStr(XxDd.ddsKc));
          flag := true;
        end;
      end;
      if flag = false then
      begin
        SDataStringList_Kc.Add('0');
      end;
    end;

    for i := 0 to CMPZSUM - 1 do
    begin
      flag := false;
      for j := 0 to StringList.Count - 1 do
      begin
        XxDd := TXxDd(StringList.Objects[j]);
        if XxDd.CmNum = i + 1 then
        begin
          SDataStringList_All.Add(IntToStr(XxDd.DdsAll));
          MATERIALFZID := XxDd.MATERIALFZID;
          cmtypenum := XxDd.cmtypenum;
          flag := true;
        end;
      end;
      if flag = false then
      begin
        SDataStringList_All.Add('0');
      end;
    end;

    for i := 0 to CMPZSUM - 1 do
    begin
      flag := false;
      for j := 0 to StringList.Count - 1 do
      begin
        XxDd := TXxDd(StringList.Objects[j]);
        if XxDd.CmNum = i + 1 then
        begin
          SData1StringList.Add(IntToStr(XxDd.Dds));
          flag := true;
        end;
      end;
      if flag = false then
      begin
        SData1StringList.Add('0');
      end;
    end;

    // SS1, SS2, SS3, SS4, SS5: Array of Integer;
    // KS1, KS2, KS3, KS4, KS5: Array of Integer;
    // SSALL1, SSALL2, SSALL3, SSALL4, SSALL5: Array of Integer;

    if k = 0 then
    begin
      setlength(SS1, CMPZSUM);
      SS1[0] := StrToInt(SData1StringList.Strings[0]);
      SS1[1] := StrToInt(SData1StringList.Strings[1]);
      SS1[2] := StrToInt(SData1StringList.Strings[2]);
      SS1[3] := StrToInt(SData1StringList.Strings[3]);
      SS1[4] := StrToInt(SData1StringList.Strings[4]);
      SS1[5] := StrToInt(SData1StringList.Strings[5]);
      SS1[6] := StrToInt(SData1StringList.Strings[6]);
      SS1[7] := StrToInt(SData1StringList.Strings[7]);
      SS1[8] := StrToInt(SData1StringList.Strings[8]);
      SS1[9] := StrToInt(SData1StringList.Strings[9]);
      SS1[10] := StrToInt(SData1StringList.Strings[10]);
      SS1[11] := StrToInt(SData1StringList.Strings[11]);
      SS1[12] := StrToInt(SData1StringList.Strings[12]);

      setlength(KS1, CMPZSUM);
      KS1[0] := StrToInt(SDataStringList_Kc.Strings[0]);
      KS1[1] := StrToInt(SDataStringList_Kc.Strings[1]);
      KS1[2] := StrToInt(SDataStringList_Kc.Strings[2]);
      KS1[3] := StrToInt(SDataStringList_Kc.Strings[3]);
      KS1[4] := StrToInt(SDataStringList_Kc.Strings[4]);
      KS1[5] := StrToInt(SDataStringList_Kc.Strings[5]);
      KS1[6] := StrToInt(SDataStringList_Kc.Strings[6]);
      KS1[7] := StrToInt(SDataStringList_Kc.Strings[7]);
      KS1[8] := StrToInt(SDataStringList_Kc.Strings[8]);
      KS1[9] := StrToInt(SDataStringList_Kc.Strings[9]);
      KS1[10] := StrToInt(SDataStringList_Kc.Strings[10]);
      KS1[11] := StrToInt(SDataStringList_Kc.Strings[11]);
      KS1[12] := StrToInt(SDataStringList_Kc.Strings[12]);

      setlength(SSALL1, CMPZSUM + 2);
      SSALL1[0] := StrToInt(SDataStringList_All.Strings[0]);
      SSALL1[1] := StrToInt(SDataStringList_All.Strings[1]);
      SSALL1[2] := StrToInt(SDataStringList_All.Strings[2]);
      SSALL1[3] := StrToInt(SDataStringList_All.Strings[3]);
      SSALL1[4] := StrToInt(SDataStringList_All.Strings[4]);
      SSALL1[5] := StrToInt(SDataStringList_All.Strings[5]);
      SSALL1[6] := StrToInt(SDataStringList_All.Strings[6]);
      SSALL1[7] := StrToInt(SDataStringList_All.Strings[7]);
      SSALL1[8] := StrToInt(SDataStringList_All.Strings[8]);
      SSALL1[9] := StrToInt(SDataStringList_All.Strings[9]);
      SSALL1[10] := StrToInt(SDataStringList_All.Strings[10]);
      SSALL1[11] := StrToInt(SDataStringList_All.Strings[11]);
      SSALL1[12] := StrToInt(SDataStringList_All.Strings[12]);
      SSALL1[13] := MATERIALFZID;
      SSALL1[14] := cmtypenum;
    end;

    if k = 1 then
    begin
      setlength(SS2, CMPZSUM);
      SS2[0] := StrToInt(SData1StringList.Strings[0]);
      SS2[1] := StrToInt(SData1StringList.Strings[1]);
      SS2[2] := StrToInt(SData1StringList.Strings[2]);
      SS2[3] := StrToInt(SData1StringList.Strings[3]);
      SS2[4] := StrToInt(SData1StringList.Strings[4]);
      SS2[5] := StrToInt(SData1StringList.Strings[5]);
      SS2[6] := StrToInt(SData1StringList.Strings[6]);
      SS2[7] := StrToInt(SData1StringList.Strings[7]);
      SS2[8] := StrToInt(SData1StringList.Strings[8]);
      SS2[9] := StrToInt(SData1StringList.Strings[9]);
      SS2[10] := StrToInt(SData1StringList.Strings[10]);
      SS2[11] := StrToInt(SData1StringList.Strings[11]);
      SS2[12] := StrToInt(SData1StringList.Strings[12]);

      setlength(KS2, CMPZSUM);
      KS2[0] := StrToInt(SDataStringList_Kc.Strings[0]);
      KS2[1] := StrToInt(SDataStringList_Kc.Strings[1]);
      KS2[2] := StrToInt(SDataStringList_Kc.Strings[2]);
      KS2[3] := StrToInt(SDataStringList_Kc.Strings[3]);
      KS2[4] := StrToInt(SDataStringList_Kc.Strings[4]);
      KS2[5] := StrToInt(SDataStringList_Kc.Strings[5]);
      KS2[6] := StrToInt(SDataStringList_Kc.Strings[6]);
      KS2[7] := StrToInt(SDataStringList_Kc.Strings[7]);
      KS2[8] := StrToInt(SDataStringList_Kc.Strings[8]);
      KS2[9] := StrToInt(SDataStringList_Kc.Strings[9]);
      KS2[10] := StrToInt(SDataStringList_Kc.Strings[10]);
      KS2[11] := StrToInt(SDataStringList_Kc.Strings[11]);
      KS2[12] := StrToInt(SDataStringList_Kc.Strings[12]);

      setlength(SSALL2, CMPZSUM + 2);
      SSALL2[0] := StrToInt(SDataStringList_All.Strings[0]);
      SSALL2[1] := StrToInt(SDataStringList_All.Strings[1]);
      SSALL2[2] := StrToInt(SDataStringList_All.Strings[2]);
      SSALL2[3] := StrToInt(SDataStringList_All.Strings[3]);
      SSALL2[4] := StrToInt(SDataStringList_All.Strings[4]);
      SSALL2[5] := StrToInt(SDataStringList_All.Strings[5]);
      SSALL2[6] := StrToInt(SDataStringList_All.Strings[6]);
      SSALL2[7] := StrToInt(SDataStringList_All.Strings[7]);
      SSALL2[8] := StrToInt(SDataStringList_All.Strings[8]);
      SSALL2[9] := StrToInt(SDataStringList_All.Strings[9]);
      SSALL2[10] := StrToInt(SDataStringList_All.Strings[10]);
      SSALL2[11] := StrToInt(SDataStringList_All.Strings[11]);
      SSALL2[12] := StrToInt(SDataStringList_All.Strings[12]);
      SSALL2[13] := MATERIALFZID;
      SSALL2[14] := cmtypenum;
    end;

    if k = 2 then
    begin
      setlength(SS3, CMPZSUM);
      SS3[0] := StrToInt(SData1StringList.Strings[0]);
      SS3[1] := StrToInt(SData1StringList.Strings[1]);
      SS3[2] := StrToInt(SData1StringList.Strings[2]);
      SS3[3] := StrToInt(SData1StringList.Strings[3]);
      SS3[4] := StrToInt(SData1StringList.Strings[4]);
      SS3[5] := StrToInt(SData1StringList.Strings[5]);
      SS3[6] := StrToInt(SData1StringList.Strings[6]);
      SS3[7] := StrToInt(SData1StringList.Strings[7]);
      SS3[8] := StrToInt(SData1StringList.Strings[8]);
      SS3[9] := StrToInt(SData1StringList.Strings[9]);
      SS3[10] := StrToInt(SData1StringList.Strings[10]);
      SS3[11] := StrToInt(SData1StringList.Strings[11]);
      SS3[12] := StrToInt(SData1StringList.Strings[12]);

      setlength(KS3, CMPZSUM);
      KS3[0] := StrToInt(SDataStringList_Kc.Strings[0]);
      KS3[1] := StrToInt(SDataStringList_Kc.Strings[1]);
      KS3[2] := StrToInt(SDataStringList_Kc.Strings[2]);
      KS3[3] := StrToInt(SDataStringList_Kc.Strings[3]);
      KS3[4] := StrToInt(SDataStringList_Kc.Strings[4]);
      KS3[5] := StrToInt(SDataStringList_Kc.Strings[5]);
      KS3[6] := StrToInt(SDataStringList_Kc.Strings[6]);
      KS3[7] := StrToInt(SDataStringList_Kc.Strings[7]);
      KS3[8] := StrToInt(SDataStringList_Kc.Strings[8]);
      KS3[9] := StrToInt(SDataStringList_Kc.Strings[9]);
      KS3[10] := StrToInt(SDataStringList_Kc.Strings[10]);
      KS3[11] := StrToInt(SDataStringList_Kc.Strings[11]);
      KS3[12] := StrToInt(SDataStringList_Kc.Strings[12]);

      setlength(SSALL3, CMPZSUM + 2);
      SSALL3[0] := StrToInt(SDataStringList_All.Strings[0]);
      SSALL3[1] := StrToInt(SDataStringList_All.Strings[1]);
      SSALL3[2] := StrToInt(SDataStringList_All.Strings[2]);
      SSALL3[3] := StrToInt(SDataStringList_All.Strings[3]);
      SSALL3[4] := StrToInt(SDataStringList_All.Strings[4]);
      SSALL3[5] := StrToInt(SDataStringList_All.Strings[5]);
      SSALL3[6] := StrToInt(SDataStringList_All.Strings[6]);
      SSALL3[7] := StrToInt(SDataStringList_All.Strings[7]);
      SSALL3[8] := StrToInt(SDataStringList_All.Strings[8]);
      SSALL3[9] := StrToInt(SDataStringList_All.Strings[9]);
      SSALL3[10] := StrToInt(SDataStringList_All.Strings[10]);
      SSALL3[11] := StrToInt(SDataStringList_All.Strings[11]);
      SSALL3[12] := StrToInt(SDataStringList_All.Strings[12]);
      SSALL3[13] := MATERIALFZID;
      SSALL3[14] := cmtypenum;
    end;

    if k = 3 then
    begin
      setlength(SS4, CMPZSUM);
      SS4[0] := StrToInt(SData1StringList.Strings[0]);
      SS4[1] := StrToInt(SData1StringList.Strings[1]);
      SS4[2] := StrToInt(SData1StringList.Strings[2]);
      SS4[3] := StrToInt(SData1StringList.Strings[3]);
      SS4[4] := StrToInt(SData1StringList.Strings[4]);
      SS4[5] := StrToInt(SData1StringList.Strings[5]);
      SS4[6] := StrToInt(SData1StringList.Strings[6]);
      SS4[7] := StrToInt(SData1StringList.Strings[7]);
      SS4[8] := StrToInt(SData1StringList.Strings[8]);
      SS4[9] := StrToInt(SData1StringList.Strings[9]);
      SS4[10] := StrToInt(SData1StringList.Strings[10]);
      SS4[11] := StrToInt(SData1StringList.Strings[11]);
      SS4[12] := StrToInt(SData1StringList.Strings[12]);

      setlength(KS4, CMPZSUM);
      KS4[0] := StrToInt(SDataStringList_Kc.Strings[0]);
      KS4[1] := StrToInt(SDataStringList_Kc.Strings[1]);
      KS4[2] := StrToInt(SDataStringList_Kc.Strings[2]);
      KS4[3] := StrToInt(SDataStringList_Kc.Strings[3]);
      KS4[4] := StrToInt(SDataStringList_Kc.Strings[4]);
      KS4[5] := StrToInt(SDataStringList_Kc.Strings[5]);
      KS4[6] := StrToInt(SDataStringList_Kc.Strings[6]);
      KS4[7] := StrToInt(SDataStringList_Kc.Strings[7]);
      KS4[8] := StrToInt(SDataStringList_Kc.Strings[8]);
      KS4[9] := StrToInt(SDataStringList_Kc.Strings[9]);
      KS4[10] := StrToInt(SDataStringList_Kc.Strings[10]);
      KS4[11] := StrToInt(SDataStringList_Kc.Strings[11]);
      KS4[12] := StrToInt(SDataStringList_Kc.Strings[12]);

      setlength(SSALL4, CMPZSUM + 2);
      SSALL4[0] := StrToInt(SDataStringList_All.Strings[0]);
      SSALL4[1] := StrToInt(SDataStringList_All.Strings[1]);
      SSALL4[2] := StrToInt(SDataStringList_All.Strings[2]);
      SSALL4[3] := StrToInt(SDataStringList_All.Strings[3]);
      SSALL4[4] := StrToInt(SDataStringList_All.Strings[4]);
      SSALL4[5] := StrToInt(SDataStringList_All.Strings[5]);
      SSALL4[6] := StrToInt(SDataStringList_All.Strings[6]);
      SSALL4[7] := StrToInt(SDataStringList_All.Strings[7]);
      SSALL4[8] := StrToInt(SDataStringList_All.Strings[8]);
      SSALL4[9] := StrToInt(SDataStringList_All.Strings[9]);
      SSALL4[10] := StrToInt(SDataStringList_All.Strings[10]);
      SSALL4[11] := StrToInt(SDataStringList_All.Strings[11]);
      SSALL4[12] := StrToInt(SDataStringList_All.Strings[12]);
      SSALL4[13] := MATERIALFZID;
      SSALL4[14] := cmtypenum;
    end;

    if k = 4 then
    begin
      setlength(SS5, CMPZSUM);
      SS5[0] := StrToInt(SData1StringList.Strings[0]);
      SS5[1] := StrToInt(SData1StringList.Strings[1]);
      SS5[2] := StrToInt(SData1StringList.Strings[2]);
      SS5[3] := StrToInt(SData1StringList.Strings[3]);
      SS5[4] := StrToInt(SData1StringList.Strings[4]);
      SS5[5] := StrToInt(SData1StringList.Strings[5]);
      SS5[6] := StrToInt(SData1StringList.Strings[6]);
      SS5[7] := StrToInt(SData1StringList.Strings[7]);
      SS5[8] := StrToInt(SData1StringList.Strings[8]);
      SS5[9] := StrToInt(SData1StringList.Strings[9]);
      SS5[10] := StrToInt(SData1StringList.Strings[10]);
      SS5[11] := StrToInt(SData1StringList.Strings[11]);
      SS5[12] := StrToInt(SData1StringList.Strings[12]);

      setlength(KS5, CMPZSUM);
      KS5[0] := StrToInt(SDataStringList_Kc.Strings[0]);
      KS5[1] := StrToInt(SDataStringList_Kc.Strings[1]);
      KS5[2] := StrToInt(SDataStringList_Kc.Strings[2]);
      KS5[3] := StrToInt(SDataStringList_Kc.Strings[3]);
      KS5[4] := StrToInt(SDataStringList_Kc.Strings[4]);
      KS5[5] := StrToInt(SDataStringList_Kc.Strings[5]);
      KS5[6] := StrToInt(SDataStringList_Kc.Strings[6]);
      KS5[7] := StrToInt(SDataStringList_Kc.Strings[7]);
      KS5[8] := StrToInt(SDataStringList_Kc.Strings[8]);
      KS5[9] := StrToInt(SDataStringList_Kc.Strings[9]);
      KS5[10] := StrToInt(SDataStringList_Kc.Strings[10]);
      KS5[11] := StrToInt(SDataStringList_Kc.Strings[11]);
      KS5[12] := StrToInt(SDataStringList_Kc.Strings[12]);

      setlength(SSALL5, CMPZSUM + 2);
      SSALL5[0] := StrToInt(SDataStringList_All.Strings[0]);
      SSALL5[1] := StrToInt(SDataStringList_All.Strings[1]);
      SSALL5[2] := StrToInt(SDataStringList_All.Strings[2]);
      SSALL5[3] := StrToInt(SDataStringList_All.Strings[3]);
      SSALL5[4] := StrToInt(SDataStringList_All.Strings[4]);
      SSALL5[5] := StrToInt(SDataStringList_All.Strings[5]);
      SSALL5[6] := StrToInt(SDataStringList_All.Strings[6]);
      SSALL5[7] := StrToInt(SDataStringList_All.Strings[7]);
      SSALL5[8] := StrToInt(SDataStringList_All.Strings[8]);
      SSALL5[9] := StrToInt(SDataStringList_All.Strings[9]);
      SSALL5[10] := StrToInt(SDataStringList_All.Strings[10]);
      SSALL5[11] := StrToInt(SDataStringList_All.Strings[11]);
      SSALL5[12] := StrToInt(SDataStringList_All.Strings[12]);
      SSALL5[13] := MATERIALFZID;
      SSALL5[14] := cmtypenum;
    end;
  end;

end;

function TThreadPdCal.GetCmFzFromCmType(cmtypenum: Integer): TStringList;
var
  StringList: TStringList;
begin
  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add
    (' select cmnum  from  cc_dm_cmxh where cmtype=:cmtype order by cmindex_b asc ');
  DMUtilSelect.UniQuery1.ParamByName('cmtype').Value := cmtypenum;
  DMUtilSelect.UniQuery1.ExecSQL;
  DMUtilSelect.UniQuery1.Active := true;

  StringList := TStringList.Create;

  while not DMUtilSelect.UniQuery1.Eof do
  begin
    StringList.Add(DMUtilSelect.UniQuery1.FieldByName('cmnum').AsString);

    DMUtilSelect.UniQuery1.Next;
  end;
  DMUtilSelect.UniQuery1.Close;

  result := StringList;
end;

function TThreadPdCal.getIndexForArrayMax(S: array of Integer;
  SUsed: array of String): Integer;
var
  i, j: Integer;
  max, maxindex: Integer;
begin
  max := 0;
  maxindex := -1;
  for i := 0 to CMPZSUM - 1 do
  begin
    if SUsed[i] <> 'Y' then
    begin
      if S[i] > 0 then
      begin
        if S[i] > max then
        begin
          max := S[i];
          maxindex := i;
        end;
      end;
    end;
  end;
  result := maxindex;

end;

function TThreadPdCal.getIndexForArrayMax1(S: array of Integer;
  SUsed: array of String): Integer;
var
  i, j: Integer;
  max, maxindex: Integer;
begin
  max := 0;
  maxindex := -1;
  for i := 0 to CMPZSUM - 1 do
  begin
    if SUsed[i] <> 'Y' then
    begin
      if S[i] > 0 then
      begin
        if S[i] >= max then
        begin
          max := S[i];
          maxindex := i;
        end;
      end;
    end;
  end;
  result := maxindex;

end;

function TThreadPdCal.getIndexForArrayMax2(S: array of Integer;
  SUsed: array of String): Integer;
var
  i, j: Integer;
  max, maxindex: Integer;
  semax, semaxindex: Integer;
begin
  max := 0;
  maxindex := -1;
  semax := 0;
  semaxindex := -1;
  for i := 0 to CMPZSUM - 1 do
  begin
    if SUsed[i] <> 'Y' then
    begin
      if S[i] > 0 then
      begin
        if S[i] > max then
        begin
          semax := max;
          semaxindex := maxindex;
          max := S[i];
          maxindex := i;
        end;
      end;
    end;
  end;
  result := semaxindex;

end;

function TThreadPdCal.getIndexForArrayMax3(S: array of Integer;
  SUsed: array of String): Integer;
var
  i, j: Integer;
  max, maxindex: Integer;
  semax, semaxindex: Integer;
begin
  max := 0;
  maxindex := -1;
  semax := 0;
  semaxindex := -1;
  for i := 0 to CMPZSUM - 1 do
  begin
    if SUsed[i] <> 'Y' then
    begin
      if S[i] > 0 then
      begin
        if S[i] >= max then
        begin
          semax := max;
          semaxindex := maxindex;
          max := S[i];
          maxindex := i;
        end;
      end;
    end;
  end;
  result := semaxindex;

end;

function TThreadPdCal.getIndexForArrayMin(S: array of Integer;
  SUsed: array of String): Integer;
var
  i, j: Integer;
  min, minindex: Integer;
begin
  min := 5000;
  minindex := -1;
  for i := 0 to CMPZSUM - 1 do
  begin
    if SUsed[i] <> 'Y' then
    begin
      if S[i] > 0 then
      begin
        if S[i] < min then
        begin
          min := S[i];
          minindex := i;
        end;
      end;
    end;
  end;
  result := minindex;

end;

function TThreadPdCal.getIndexForArrayMin1(S: array of Integer): Integer;
var
  i, j: Integer;
  min, minindex: Integer;
begin
  min := 5000;
  minindex := -1;
  for i := 0 to CMPZSUM - 1 do
  begin
    if S[i] > 0 then
    begin
      if S[i] < min then
      begin
        min := S[i];
        minindex := i;
      end;
    end;
  end;
  result := minindex;

end;

function TThreadPdCal.getMfForArray(DCount: Integer; D, C: array of Integer;
  RoleNum: Integer): Integer;
var
  flag: Integer;
  i: Integer;
  Count: Integer;
  Sum: Integer;
  Pjfz: double;
begin
  flag := 0;

  Sum := 0;
  Count := 0;
  for i := 0 to CMPZSUM - 1 do
  begin
    if (D[i] > 0) then
    begin
      Sum := Sum + (D[i] div RoleNum) * C[i];
    end;
  end;
  if DCount > 0 then
  begin
    Pjfz := Sum / DCount;
    if (Pjfz >= FzMin_G) and (Pjfz <= FzMax_G) then
    begin
      flag := 0;
    end
    else
    begin
      if Pjfz < FzMin_G then
      begin
        flag := 1;
      end;
      if Pjfz > FzMax_G then
      begin
        flag := 2;
      end;
    end;
  end;

  result := flag;

end;

function TThreadPdCal.getMfForArray1(DCount: Integer; D, C: array of Integer;
  RoleNum: Integer): Integer;
var
  flag: Integer;
  i: Integer;
  Count: Integer;
  Sum: Integer;
  Pjfz: double;
begin
  flag := 0;

  Sum := 0;
  Count := 0;
  for i := 0 to CMPZSUM - 1 do
  begin
    if (D[i] > 0) then
    begin
      Sum := Sum + (D[i] div RoleNum) * C[i];
    end;
  end;
  if DCount > 0 then
  begin
    Pjfz := Sum / DCount;
    if (Pjfz >= FzMin_G) and (Pjfz <= FzMax_G) then
    begin
      flag := 0;
    end
    else
    begin
      if Pjfz < FzMin_G then
      begin
        flag := 1;
      end;
      if Pjfz > FzMax_G then
      begin
        flag := 2;
      end;
    end;
  end;

  result := flag;

end;

function TThreadPdCal.getNumberForArrayMax(SUsed: array of String): Integer;
var
  i, j: Integer;
  Sum: Integer;
begin
  Sum := 0;
  for i := 0 to CMPZSUM - 1 do
  begin
    if SUsed[i] = 'Y' then
    begin
      Sum := Sum + 1;
    end;
  end;
  result := Sum;

end;

procedure TThreadPdCal.InitDetail(MaterialidStringList: TStringList;
  Fztypenum: Integer; kstypesecid: Integer;
  aDMUtilSave, aDMUtilSelect: TDataModule1);
var
  C: array of Integer;
  StringList: TStringList;
  i, j: Integer;
  XxDd: TXxDd;
  ManStringList: TStringList;
  WomenStringList: TStringList;
  ChildStringList: TStringList;
  Cm: string;
  flag: Boolean;
  CcMaterial: TCcMaterial;
  MaterialIdStr: string;
  cmtypenum: string;
begin

  G_kstypesecid := kstypesecid;

  DMUtilSave := aDMUtilSave;
  DMUtilSelect := aDMUtilSelect;

  FmulFlag := false;

  Source_Data1StringList := TStringList.Create;
  Source_Data2StringList := TStringList.Create;
  Source_Data3StringList := TStringList.Create;
  Source_Data4StringList := TStringList.Create;
  Source_Data5StringList := TStringList.Create;

  // 读取原始数据到各Source_DataStringList中
  // 合并计算，包括合并计算库存
  MaterialIdStr := '(';
  for i := 0 to MaterialidStringList.Count - 1 do
  begin
    CcMaterial := TCcMaterial(MaterialidStringList.Objects[i]);

    MaterialIdStr := MaterialIdStr + IntToStr(CcMaterial.Materialid);
    MaterialIdStr := MaterialIdStr + ',';
    // G_KcFlag := false;

    DMUtilSelect.UniQuery1.Close;
    DMUtilSelect.UniQuery1.Sql.Clear;
    DMUtilSelect.UniQuery1.Sql.Add('select * from (select case when locate(' +
      '''' + '男' + '''' + ',xx_dd_detail.ddlb)>0 then 1 ' + ' when locate(' +
      '''' + '女' + '''' + ',xx_dd_detail.ddlb)>0 then 2  ' + ' when locate(' +
      '''' + '儿' + '''' +
      ',xx_dd_detail.ddlb)>0 then 3 end cmtypenum,CC_MATERIALFZMAIN.MATERIALFZID,xx_dd_detail.dds,xx_dd_detail.dds_kc,xx_dd_detail.ddcm  '
      + ' from xx_dd_detail,xx_dd,CC_MATERIALFZMAIN,CC_MATERIALFZDETAIL,cc_material '
      + ' where xx_dd.ddid = xx_dd_detail.ddid and cc_materialfzdetail.MATERIALID=cc_material.MATERIALID and xx_dd.ddid = cc_material.ddid  '
      + ' and CC_MATERIALFZMAIN.MATERIALFZID = CC_MATERIALFZDETAIL.MATERIALFZID and cc_material.MATERIALID=:MATERIALID and CC_MATERIALFZMAIN.FZTYPENUM=:FZTYPENUM) a,cc_dm_cmxh  '
      + ' where  cmtypenum=cc_dm_cmxh.cmtype and ddcm=cc_dm_cmxh.cm ');
    DMUtilSelect.UniQuery1.ParamByName('MATERIALID').Value :=
      CcMaterial.Materialid;
    DMUtilSelect.UniQuery1.ParamByName('FZTYPENUM').Value := Fztypenum;
    DMUtilSelect.UniQuery1.ExecSQL;
    DMUtilSelect.UniQuery1.Active := true;

    j := 0;
    while not DMUtilSelect.UniQuery1.Eof do
    begin
      j := j + 1;

      XxDd := TXxDd.Create;
      XxDd.CmNum := DMUtilSelect.UniQuery1.FieldByName('cmindex_b').AsInteger;
      XxDd.Dds := DMUtilSelect.UniQuery1.FieldByName('dds').AsInteger -
        DMUtilSelect.UniQuery1.FieldByName('dds_kc').AsInteger;
      XxDd.ddsKc := DMUtilSelect.UniQuery1.FieldByName('dds_kc').AsInteger;
      XxDd.DdsAll := DMUtilSelect.UniQuery1.FieldByName('dds').AsInteger;
      XxDd.MATERIALFZID := DMUtilSelect.UniQuery1.FieldByName('MATERIALFZID')
        .AsInteger;
      XxDd.cmtypenum := DMUtilSelect.UniQuery1.FieldByName('cmtypenum')
        .AsInteger;
      // if XxDd.ddsKc > 0 then
      // begin
      // G_KcFlag := true;
      // end;

      XxDd.Cm := DMUtilSelect.UniQuery1.FieldByName('ddcm').AsString;

      if i = 0 then
      begin
        Source_Data1StringList.AddObject(IntToStr(j), XxDd);
      end;

      if i = 1 then
      begin
        Source_Data2StringList.AddObject(IntToStr(j), XxDd);
      end;

      if i = 2 then
      begin
        Source_Data3StringList.AddObject(IntToStr(j), XxDd);
      end;

      if i = 3 then
      begin
        Source_Data4StringList.AddObject(IntToStr(j), XxDd);
      end;

      if i = 4 then
      begin
        Source_Data5StringList.AddObject(IntToStr(j), XxDd);
      end;
      G_CmTypenum := XxDd.cmtypenum;

      DMUtilSelect.UniQuery1.Next;
    end;
    DMUtilSelect.UniQuery1.Close;

  end;

  Delete(MaterialIdStr, Length(MaterialIdStr), Length(MaterialIdStr) + 1);
  MaterialIdStr := MaterialIdStr + ')';

  // 进行数据合并

  StringList := TStringList.Create;

  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;
  DMUtilSelect.UniQuery1.Sql.Add
    ('select sum(dds) dds, sum(dds_kc) dds_kc,cmindex_b from (select case when locate('
    + '''' + '男' + '''' + ',xx_dd_detail.ddlb)>0 then 1 ' + ' when locate(' +
    '''' + '女' + '''' + ',xx_dd_detail.ddlb)>0 then 2  ' + ' when locate(' +
    '''' + '儿' + '''' +
    ',xx_dd_detail.ddlb)>0 then 3 end cmtypenum, CC_MATERIALFZMAIN.MATERIALFZID,xx_dd_detail.dds,xx_dd_detail.dds_kc,xx_dd_detail.ddcm  '
    + ' from xx_dd_detail,xx_dd,CC_MATERIALFZMAIN,CC_MATERIALFZDETAIL,cc_material  '
    + ' where xx_dd.ddid = xx_dd_detail.ddid and cc_materialfzdetail.MATERIALID=cc_material.MATERIALID and xx_dd.ddid = cc_material.ddid  '
    + ' and CC_MATERIALFZMAIN.MATERIALFZID = CC_MATERIALFZDETAIL.MATERIALFZID and cc_material.MATERIALID in '
    + MaterialIdStr +
    ' and CC_MATERIALFZMAIN.FZTYPENUM=:FZTYPENUM) a,cc_dm_cmxh  ' +
    ' where  cmtypenum=cc_dm_cmxh.cmtype and ddcm=cc_dm_cmxh.cm  ' +
    ' group by cc_dm_cmxh.cmindex_b ');
  DMUtilSelect.UniQuery1.ParamByName('FZTYPENUM').Value := Fztypenum;
  DMUtilSelect.UniQuery1.ExecSQL;
  DMUtilSelect.UniQuery1.Active := true;

  j := 0;
  while not DMUtilSelect.UniQuery1.Eof do
  begin
    j := j + 1;
    XxDd := TXxDd.Create;
    XxDd.Dds := DMUtilSelect.UniQuery1.FieldByName('dds').AsInteger -
      DMUtilSelect.UniQuery1.FieldByName('dds_kc').AsInteger;
    XxDd.ddsKc := DMUtilSelect.UniQuery1.FieldByName('dds_kc').AsInteger;
    XxDd.DdsAll := DMUtilSelect.UniQuery1.FieldByName('dds').AsInteger;
    XxDd.CmNum := DMUtilSelect.UniQuery1.FieldByName('cmindex_b').AsInteger;

    StringList.AddObject(IntToStr(j), XxDd);
    DMUtilSelect.UniQuery1.Next;
  end;
  DMUtilSelect.UniQuery1.Close;

  // 0 1 2 4	6	8	10	12	14  16
  // 6	8	10	12	14	16	18	20	22	24  26  28
  // 3XS 2XS XS	S	M	L	XL	2XL	3XL	4XL	5XL	7XL 9XL

  // 计算库存进行存储
  DataStringList_Kc := TStringList.Create;

  for i := 0 to CMPZSUM - 1 do
  begin
    flag := false;
    for j := 0 to StringList.Count - 1 do
    begin
      XxDd := TXxDd(StringList.Objects[j]);
      if XxDd.CmNum = i + 1 then
      begin
        DataStringList_Kc.Add(IntToStr(XxDd.ddsKc));
        flag := true;
      end;
    end;
    if flag = false then
    begin
      DataStringList_Kc.Add('0');
    end;
  end;

  DataStringList_All := TStringList.Create;

  for i := 0 to CMPZSUM - 1 do
  begin
    flag := false;
    for j := 0 to StringList.Count - 1 do
    begin
      XxDd := TXxDd(StringList.Objects[j]);
      if XxDd.CmNum = i + 1 then
      begin
        DataStringList_All.Add(IntToStr(XxDd.DdsAll));
        flag := true;
      end;
    end;
    if flag = false then
    begin
      DataStringList_All.Add('0');
    end;
  end;

  Data1StringList := TStringList.Create;

  for i := 0 to CMPZSUM - 1 do
  begin
    flag := false;
    for j := 0 to StringList.Count - 1 do
    begin
      XxDd := TXxDd(StringList.Objects[j]);
      if XxDd.CmNum = i + 1 then
      begin
        Data1StringList.Add(IntToStr(XxDd.Dds));
        flag := true;
      end;
    end;
    if flag = false then
    begin
      Data1StringList.Add('0');
    end;
  end;

  Data2StringList := TStringList.Create;
  Data2StringList.Add('0');
  Data2StringList.Add('0');
  Data2StringList.Add('0');
  Data2StringList.Add('0');
  Data2StringList.Add('0');
  Data2StringList.Add('0');
  Data2StringList.Add('0');
  Data2StringList.Add('0');
  Data2StringList.Add('0');
  Data2StringList.Add('0');
  Data2StringList.Add('0');
  Data2StringList.Add('0');

  Data3StringList := TStringList.Create;
  Data3StringList.Add('0');
  Data3StringList.Add('0');
  Data3StringList.Add('0');
  Data3StringList.Add('0');
  Data3StringList.Add('0');
  Data3StringList.Add('0');
  Data3StringList.Add('0');
  Data3StringList.Add('0');
  Data3StringList.Add('0');
  Data3StringList.Add('0');
  Data3StringList.Add('0');
  Data3StringList.Add('0');

  FormatStrings;

  G_Js := '7';
  G_Cs := '200';
  G_bc := 0;
  G_MaxTg := 380;
  G_Gz1 := '';
  G_YJL := 0;
  G_ZLYJL := 0;
  G_Fz_B := '1';
  G_Fz_E := '10';

  // 取得最大件数和层数
  DMUtilSelect.UniQuery1.Close;
  DMUtilSelect.UniQuery1.Sql.Clear;

  if G_kstypesecid = 1 then
  begin
    if Fztypenum = 1 then
    begin
      DMUtilSelect.UniQuery1.Sql.Add
        ('select max(ITEM_1_1_5) cs,max(ITEM_1_1_6) js,max(ITEM_1_1_8) bc,max(ITEM_1_1_18) MaxTg,max(ITEM_1_1_19) Gz1,min(ITEM_1_1_15) Fz_b,max(ITEM_1_1_16) Fz_e,'
        + ' max(ifnull(ITEM_1_1_7,0)+ifnull(ITEM_1_1_9,0)+ifnull(ITEM_1_1_10,0)+ifnull(ITEM_1_1_11,0)+ifnull(ITEM_1_1_12,0)+ifnull(ITEM_1_1_13,0)+ifnull(ITEM_1_1_14,0)) YJL,max(ifnull(ITEM_1_1_7,0)) ZLYJL from dm_ksmaterialrecord_1,xx_ks,xx_dd,cc_material '
        + ' where cc_material.ddid = xx_dd.ddid and dm_ksmaterialrecord_1.ksid = xx_ks.ksid and xx_ks.kh = xx_dd.kh and xx_ks.kz = xx_dd.ddkz and xx_ks.lb = xx_dd.ddlb '
        + ' and xx_ks.ml = xx_dd.ddml and xx_ks.ks = xx_dd.ddks and xx_ks.ys = xx_dd.ddys and xx_ks.ksbm = xx_dd.ksbm and cc_material.materialid in '
        + MaterialIdStr);
    end;
    if Fztypenum = 2 then
    begin
      DMUtilSelect.UniQuery1.Sql.Add
        ('select max(ITEM_1_2_5) cs,max(ITEM_1_2_6) js,max(ITEM_1_2_8) bc,max(ITEM_1_2_18) MaxTg,max(ITEM_1_2_19) Gz1,min(ITEM_1_2_15) Fz_b,max(ITEM_1_2_16) Fz_e,'
        + ' max(ifnull(ITEM_1_2_7,0)+ifnull(ITEM_1_2_9,0)+ifnull(ITEM_1_2_10,0)+ifnull(ITEM_1_2_11,0)+ifnull(ITEM_1_2_12,0)+ifnull(ITEM_1_2_13,0)+ifnull(ITEM_1_2_14,0)) YJL,max(ifnull(ITEM_1_2_7,0)) ZLYJL from dm_ksmaterialrecord_1,xx_ks,xx_dd,cc_material '
        + ' where cc_material.ddid = xx_dd.ddid and dm_ksmaterialrecord_1.ksid = xx_ks.ksid and xx_ks.kh = xx_dd.kh and xx_ks.kz = xx_dd.ddkz and xx_ks.lb = xx_dd.ddlb '
        + ' and xx_ks.ml = xx_dd.ddml and xx_ks.ks = xx_dd.ddks and xx_ks.ys = xx_dd.ddys and xx_ks.ksbm = xx_dd.ksbm and cc_material.materialid in '
        + MaterialIdStr);
    end;
    if Fztypenum = 3 then
    begin
      DMUtilSelect.UniQuery1.Sql.Add
        ('select max(ITEM_1_3_5) cs,max(ITEM_1_3_6) js,max(ITEM_1_3_8) bc,max(ITEM_1_3_16) MaxTg,max(ITEM_1_3_17) Gz1,min(ITEM_1_3_15) Fz_b,max(ITEM_1_3_16) Fz_e,'
        + ' max(ifnull(ITEM_1_3_7,0)+ifnull(ITEM_1_3_9,0)+ifnull(ITEM_1_3_10,0)+ifnull(ITEM_1_3_11,0)+ifnull(ITEM_1_3_12,0)+ifnull(ITEM_1_3_13,0)+ifnull(ITEM_1_3_14,0)) YJL,'
        + ' max(ifnull(ITEM_1_3_7,0)) ZLYJL from dm_ksmaterialrecord_1,xx_ks,xx_dd,cc_material '
        + ' where cc_material.ddid = xx_dd.ddid and dm_ksmaterialrecord_1.ksid = xx_ks.ksid and xx_ks.kh = xx_dd.kh and xx_ks.kz = xx_dd.ddkz and xx_ks.lb = xx_dd.ddlb '
        + ' and xx_ks.ml = xx_dd.ddml and xx_ks.ks = xx_dd.ddks and xx_ks.ys = xx_dd.ddys and xx_ks.ksbm = xx_dd.ksbm and cc_material.materialid in '
        + MaterialIdStr);
    end;
    if Fztypenum = 4 then
    begin
      DMUtilSelect.UniQuery1.Sql.Add
        ('select max(ITEM_1_4_5) cs,max(ITEM_1_4_6) js,max(ITEM_1_4_8) bc,max(ITEM_1_4_16) MaxTg,max(ITEM_1_4_17) Gz1,min(ITEM_1_4_12) Fz_b,max(ITEM_1_4_13) Fz_e,'
        + ' max(ifnull(ITEM_1_4_7,0)+ifnull(ITEM_1_4_9,0)+ifnull(ITEM_1_4_10,0)*1/100*0.32+ifnull(ITEM_1_4_11,0)) YJL,max(ifnull(ITEM_1_4_7,0)) ZLYJL from dm_ksmaterialrecord_1,xx_ks,xx_dd,cc_material '
        + ' where cc_material.ddid = xx_dd.ddid and dm_ksmaterialrecord_1.ksid = xx_ks.ksid and xx_ks.kh = xx_dd.kh and xx_ks.kz = xx_dd.ddkz and xx_ks.lb = xx_dd.ddlb '
        + ' and xx_ks.ml = xx_dd.ddml and xx_ks.ks = xx_dd.ddks and xx_ks.ys = xx_dd.ddys and xx_ks.ksbm = xx_dd.ksbm and cc_material.materialid in '
        + MaterialIdStr);
    end;

    if Fztypenum = 5 then
    begin
      DMUtilSelect.UniQuery1.Sql.Add
        ('select max(ITEM_1_5_5) cs,max(ITEM_1_5_6) js,max(ITEM_1_5_8) bc,max(ITEM_1_5_16) MaxTg,max(ITEM_1_5_17) Gz1,min(ITEM_1_5_15) Fz_b,max(ITEM_1_5_16) Fz_e,'
        + ' max(ifnull(ITEM_1_5_7,0)+ifnull(ITEM_1_5_9,0)+ifnull(ITEM_1_5_10,0)*1/100*0.32+ifnull(ITEM_1_5_11,0)*0.9/100*0.32+ifnull(ITEM_1_5_12,0)+ifnull(ITEM_1_5_13,0)+ifnull(ITEM_1_5_14,0)) YJL,'
        + ' max(ifnull(ITEM_1_5_7,0)) ZLYJL from dm_ksmaterialrecord_1,xx_ks,xx_dd,cc_material '
        + ' where cc_material.ddid = xx_dd.ddid and dm_ksmaterialrecord_1.ksid = xx_ks.ksid and xx_ks.kh = xx_dd.kh and xx_ks.kz = xx_dd.ddkz and xx_ks.lb = xx_dd.ddlb '
        + ' and xx_ks.ml = xx_dd.ddml and xx_ks.ks = xx_dd.ddks and xx_ks.ys = xx_dd.ddys and xx_ks.ksbm = xx_dd.ksbm and cc_material.materialid in '
        + MaterialIdStr);
    end;
    if Fztypenum = 6 then
    begin
      DMUtilSelect.UniQuery1.Sql.Add
        ('select max(ITEM_1_6_5) cs,max(ITEM_1_6_6) js,max(ITEM_1_6_8) bc,max(ITEM_1_6_16) MaxTg,max(ITEM_1_6_17) Gz1,min(ITEM_1_6_12) Fz_b,max(ITEM_1_6_13) Fz_e,'
        + ' max(ifnull(ITEM_1_6_7,0)+ifnull(ITEM_1_6_9,0)+ifnull(ITEM_1_6_10,0)*1/100*0.32+ifnull(ITEM_1_6_11,0)) YJL,max(ifnull(ITEM_1_6_7,0)) ZLYJL from dm_ksmaterialrecord_1,xx_ks,xx_dd,cc_material '
        + ' where cc_material.ddid = xx_dd.ddid and dm_ksmaterialrecord_1.ksid = xx_ks.ksid and xx_ks.kh = xx_dd.kh and xx_ks.kz = xx_dd.ddkz and xx_ks.lb = xx_dd.ddlb '
        + ' and xx_ks.ml = xx_dd.ddml and xx_ks.ks = xx_dd.ddks and xx_ks.ys = xx_dd.ddys and xx_ks.ksbm = xx_dd.ksbm and cc_material.materialid in '
        + MaterialIdStr);
    end;

  end;

  if G_kstypesecid = 2 then
  begin
    if Fztypenum = 1 then
    begin
      DMUtilSelect.UniQuery1.Sql.Add
        ('select max(ITEM_2_1_5) cs,max(ITEM_2_1_6) js,max(ITEM_2_1_8) bc,max(ITEM_2_1_19) MaxTg,max(ITEM_2_1_20) Gz1,min(ITEM_2_1_16) Fz_b,max(ITEM_2_1_17) Fz_e,'
        + ' max(ifnull(ITEM_2_1_7,0)+ifnull(ITEM_2_1_9,0)+ifnull(ITEM_2_1_10,0)+ifnull(ITEM_2_1_11,0)+ifnull(ITEM_2_1_12,0)+ifnull(ITEM_2_1_13,0)+ifnull(ITEM_2_1_14,0)+ifnull(ITEM_2_1_15,0)) YJL,'
        + ' max(ifnull(ITEM_2_1_7,0)) ZLYJL from dm_ksmaterialrecord_2,xx_ks,xx_dd,cc_material '
        + ' where cc_material.ddid = xx_dd.ddid and dm_ksmaterialrecord_2.ksid = xx_ks.ksid and xx_ks.kh = xx_dd.kh and xx_ks.kz = xx_dd.ddkz and xx_ks.lb = xx_dd.ddlb '
        + ' and xx_ks.ml = xx_dd.ddml and xx_ks.ks = xx_dd.ddks and xx_ks.ys = xx_dd.ddys and xx_ks.ksbm = xx_dd.ksbm and cc_material.materialid in '
        + MaterialIdStr);
    end;
    if Fztypenum = 2 then
    begin
      DMUtilSelect.UniQuery1.Sql.Add
        ('select max(ITEM_2_2_5) cs,max(ITEM_2_2_6) js,max(ITEM_2_2_8) bc,max(ITEM_2_2_18) MaxTg,max(ITEM_2_2_19) Gz1,min(ITEM_2_2_15) Fz_b,max(ITEM_2_2_16) Fz_e,'
        + ' max(ifnull(ITEM_2_2_7,0)+ifnull(ITEM_2_2_9,0)+ifnull(ITEM_2_2_10,0)+ifnull(ITEM_2_2_11,0)+ifnull(ITEM_2_2_12,0)+ifnull(ITEM_2_2_13,0)+ifnull(ITEM_2_2_14,0)) YJL,max(ifnull(ITEM_2_2_7,0)) ZLYJL from dm_ksmaterialrecord_2,xx_ks,xx_dd,cc_material '
        + ' where cc_material.ddid = xx_dd.ddid and dm_ksmaterialrecord_2.ksid = xx_ks.ksid and xx_ks.kh = xx_dd.kh and xx_ks.kz = xx_dd.ddkz and xx_ks.lb = xx_dd.ddlb '
        + ' and xx_ks.ml = xx_dd.ddml and xx_ks.ks = xx_dd.ddks and xx_ks.ys = xx_dd.ddys and xx_ks.ksbm = xx_dd.ksbm and cc_material.materialid in '
        + MaterialIdStr);
    end;
    if Fztypenum = 3 then
    begin
      DMUtilSelect.UniQuery1.Sql.Add
        ('select max(ITEM_2_3_5) cs,max(ITEM_2_3_6) js,max(ITEM_2_3_8) bc,max(ITEM_2_3_16) MaxTg,max(ITEM_2_3_17) Gz1,min(ITEM_2_3_13) Fz_b,max(ITEM_2_3_14) Fz_e,'
        + ' max(ifnull(ITEM_2_3_7,0)+ifnull(ITEM_2_3_9,0)+ifnull(ITEM_2_3_10,0)+ifnull(ITEM_2_3_11,0)+ifnull(ITEM_2_3_12,0)) YJL,max(ifnull(ITEM_2_3_7,0)) ZLYJL from dm_ksmaterialrecord_2,xx_ks,xx_dd,cc_material '
        + ' where cc_material.ddid = xx_dd.ddid and dm_ksmaterialrecord_2.ksid = xx_ks.ksid and xx_ks.kh = xx_dd.kh and xx_ks.kz = xx_dd.ddkz and xx_ks.lb = xx_dd.ddlb '
        + ' and xx_ks.ml = xx_dd.ddml and xx_ks.ks = xx_dd.ddks and xx_ks.ys = xx_dd.ddys and xx_ks.ksbm = xx_dd.ksbm and cc_material.materialid in '
        + MaterialIdStr);
    end;
    if Fztypenum = 4 then
    begin
      DMUtilSelect.UniQuery1.Sql.Add
        ('select max(ITEM_2_4_5) cs,max(ITEM_2_4_6) js,max(ITEM_2_4_8) bc,max(ITEM_2_4_16) MaxTg,max(ITEM_2_4_17) Gz1,min(ITEM_2_4_13) Fz_b,max(ITEM_2_4_14) Fz_e,'
        + ' max(ifnull(ITEM_2_4_7,0)+ifnull(ITEM_2_4_9,0)+ifnull(ITEM_2_4_10,0)+ifnull(ITEM_2_4_11,0)+ifnull(ITEM_2_4_12,0)) YJL,max(ifnull(ITEM_2_4_7,0)) ZLYJL from dm_ksmaterialrecord_2,xx_ks,xx_dd,cc_material '
        + ' where cc_material.ddid = xx_dd.ddid and dm_ksmaterialrecord_2.ksid = xx_ks.ksid and xx_ks.kh = xx_dd.kh and xx_ks.kz = xx_dd.ddkz and xx_ks.lb = xx_dd.ddlb '
        + ' and xx_ks.ml = xx_dd.ddml and xx_ks.ks = xx_dd.ddks and xx_ks.ys = xx_dd.ddys and xx_ks.ksbm = xx_dd.ksbm and cc_material.materialid in '
        + MaterialIdStr);
    end;
    if Fztypenum = 5 then
    begin
      DMUtilSelect.UniQuery1.Sql.Add
        ('select max(ITEM_2_5_5) cs,max(ITEM_2_5_6) js,max(ITEM_2_5_8) bc,max(ITEM_2_5_16) MaxTg,max(ITEM_2_5_17) Gz1,min(ITEM_2_5_15) Fz_b,max(ITEM_2_5_16) Fz_e,'
        + ' max(ifnull(ITEM_2_5_7,0)+ifnull(ITEM_2_5_9,0)+ifnull(ITEM_2_5_10,0)*1/100*0.32+ifnull(ITEM_2_5_11,0)*0.9/100*0.32+ifnull(ITEM_2_5_12,0)+ifnull(ITEM_2_5_13,0)+ifnull(ITEM_2_5_14,0)) YJL,'
        + ' max(ifnull(ITEM_2_5_7,0)) ZLYJL from dm_ksmaterialrecord_2,xx_ks,xx_dd,cc_material '
        + ' where cc_material.ddid = xx_dd.ddid and dm_ksmaterialrecord_2.ksid = xx_ks.ksid and xx_ks.kh = xx_dd.kh and xx_ks.kz = xx_dd.ddkz and xx_ks.lb = xx_dd.ddlb '
        + ' and xx_ks.ml = xx_dd.ddml and xx_ks.ks = xx_dd.ddks and xx_ks.ys = xx_dd.ddys and xx_ks.ksbm = xx_dd.ksbm and cc_material.materialid in '
        + MaterialIdStr);
    end;
    if Fztypenum = 6 then
    begin
      DMUtilSelect.UniQuery1.Sql.Add
        ('select max(ITEM_2_6_5) cs,max(ITEM_2_6_6) js,max(ITEM_2_6_8) bc,max(ITEM_2_6_16) MaxTg,max(ITEM_2_6_17) Gz1,min(ITEM_2_6_15) Fz_b,max(ITEM_2_6_16) Fz_e,'
        + ' max(ifnull(ITEM_2_6_7,0)+ifnull(ITEM_2_6_9,0)+ifnull(ITEM_2_6_10,0)*1/100*0.32+ifnull(ITEM_2_6_11,0)*0.9/100*0.32+ifnull(ITEM_2_6_12,0)+ifnull(ITEM_2_6_13,0)+ifnull(ITEM_2_6_14,0)) YJL,'
        + ' max(ifnull(ITEM_2_6_7,0)) ZLYJL from dm_ksmaterialrecord_2,xx_ks,xx_dd,cc_material '
        + ' where cc_material.ddid = xx_dd.ddid and dm_ksmaterialrecord_2.ksid = xx_ks.ksid and xx_ks.kh = xx_dd.kh and xx_ks.kz = xx_dd.ddkz and xx_ks.lb = xx_dd.ddlb '
        + ' and xx_ks.ml = xx_dd.ddml and xx_ks.ks = xx_dd.ddks and xx_ks.ys = xx_dd.ddys and xx_ks.ksbm = xx_dd.ksbm and cc_material.materialid in '
        + MaterialIdStr);
    end;
  end;

  if G_kstypesecid = 3 then
  begin
    if Fztypenum = 1 then
    begin
      DMUtilSelect.UniQuery1.Sql.Add
        ('select max(ITEM_3_1_5) cs,max(ITEM_3_1_6) js,max(ITEM_3_1_8) bc,max(ITEM_3_1_15) MaxTg,max(ITEM_3_1_16) Gz1,min(ITEM_3_1_12) Fz_b,max(ITEM_3_1_13) Fz_e,'
        + ' max(ifnull(ITEM_3_1_7,0)+ifnull(ITEM_3_1_9,0)*1/100*0.5+ifnull(ITEM_3_1_10,0)+ifnull(ITEM_3_1_11,0)) YJL,max(ifnull(ITEM_3_1_7,0)) ZLYJL from dm_ksmaterialrecord_3,xx_ks,xx_dd,cc_material '
        + ' where cc_material.ddid = xx_dd.ddid and dm_ksmaterialrecord_3.ksid = xx_ks.ksid and xx_ks.kh = xx_dd.kh and xx_ks.kz = xx_dd.ddkz and xx_ks.lb = xx_dd.ddlb '
        + ' and xx_ks.ml = xx_dd.ddml and xx_ks.ks = xx_dd.ddks and xx_ks.ys = xx_dd.ddys and xx_ks.ksbm = xx_dd.ksbm and cc_material.materialid in '
        + MaterialIdStr);
    end;
    if Fztypenum = 2 then
    begin
      DMUtilSelect.UniQuery1.Sql.Add
        ('select max(ITEM_3_2_5) cs,max(ITEM_3_2_6) js,max(ITEM_3_2_8) bc,max(ITEM_3_2_19) MaxTg,max(ITEM_3_2_20) Gz1,min(ITEM_3_2_16) Fz_b,max(ITEM_3_2_17) Fz_e,'
        + ' max(ifnull(ITEM_3_2_7,0)+ifnull(ITEM_3_2_9,0)*0.32+ifnull(ITEM_3_2_10,0)+ifnull(ITEM_3_2_11,0)+ifnull(ITEM_3_2_12,0)*1/100*0.32+ifnull(ITEM_3_2_13,0)*0.9/100*0.32+'
        + ' ifnull(ITEM_3_2_14,0)+ifnull(ITEM_3_2_15,0)) YJL,max(ifnull(ITEM_3_2_7,0)) ZLYJL from dm_ksmaterialrecord_3,xx_ks,xx_dd,cc_material '
        + ' where cc_material.ddid = xx_dd.ddid and dm_ksmaterialrecord_3.ksid = xx_ks.ksid and xx_ks.kh = xx_dd.kh and xx_ks.kz = xx_dd.ddkz and xx_ks.lb = xx_dd.ddlb '
        + ' and xx_ks.ml = xx_dd.ddml and xx_ks.ks = xx_dd.ddks and xx_ks.ys = xx_dd.ddys and xx_ks.ksbm = xx_dd.ksbm and cc_material.materialid in '
        + MaterialIdStr);
    end;
  end;

  if G_kstypesecid = 4 then
  begin
    if Fztypenum = 1 then
    begin
      DMUtilSelect.UniQuery1.Sql.Add
        ('select max(ITEM_4_1_5) cs,max(ITEM_4_1_6) js,max(ITEM_4_1_8) bc,max(ITEM_4_1_18) MaxTg,max(ITEM_4_1_19) Gz1,min(ITEM_4_1_15) Fz_b,max(ITEM_4_1_16) Fz_e,'
        + ' max(ifnull(ITEM_4_1_7,0)+ifnull(ITEM_4_1_9,0)*0.32+ifnull(ITEM_4_1_10,0)+ifnull(ITEM_4_1_11,0)*1/100*0.32+ifnull(ITEM_4_1_12,0)*4.7/100*0.32+ifnull(ITEM_4_1_13,0)+ifnull(ITEM_4_1_14,0)) YJL,'
        + ' max(ifnull(ITEM_4_1_7,0)) ZLYJL from dm_ksmaterialrecord_4,xx_ks,xx_dd,cc_material '
        + ' where cc_material.ddid = xx_dd.ddid and dm_ksmaterialrecord_4.ksid = xx_ks.ksid and xx_ks.kh = xx_dd.kh and xx_ks.kz = xx_dd.ddkz and xx_ks.lb = xx_dd.ddlb '
        + ' and xx_ks.ml = xx_dd.ddml and xx_ks.ks = xx_dd.ddks and xx_ks.ys = xx_dd.ddys and xx_ks.ksbm = xx_dd.ksbm and cc_material.materialid in '
        + MaterialIdStr);
    end;
    if Fztypenum = 2 then
    begin
      DMUtilSelect.UniQuery1.Sql.Add
        ('select max(ITEM_4_2_5) cs,max(ITEM_4_2_6) js,max(ITEM_4_2_8) bc,max(ITEM_4_2_18) MaxTg,max(ITEM_4_2_19) Gz1,min(ITEM_4_2_15) Fz_b,max(ITEM_4_2_16) Fz_e,'
        + ' max(ifnull(ITEM_4_2_7,0)+ifnull(ITEM_4_2_9,0)*0.32+ifnull(ITEM_4_2_10,0)*1/100*0.32+ifnull(ITEM_4_2_11,0)*4.2/100*0.32+ifnull(ITEM_4_2_12,0)*4.7/100*0.32+ifnull(ITEM_4_2_13,0)+ifnull(ITEM_4_2_14,0)) YJL,'
        + ' max(ifnull(ITEM_4_2_7,0)) ZLYJL from dm_ksmaterialrecord_4,xx_ks,xx_dd,cc_material '
        + ' where cc_material.ddid = xx_dd.ddid and dm_ksmaterialrecord_4.ksid = xx_ks.ksid and xx_ks.kh = xx_dd.kh and xx_ks.kz = xx_dd.ddkz and xx_ks.lb = xx_dd.ddlb '
        + ' and xx_ks.ml = xx_dd.ddml and xx_ks.ks = xx_dd.ddks and xx_ks.ys = xx_dd.ddys and xx_ks.ksbm = xx_dd.ksbm and cc_material.materialid in '
        + MaterialIdStr);
    end;
  end;

  if G_kstypesecid = 5 then
  begin
    if Fztypenum = 1 then
    begin
      DMUtilSelect.UniQuery1.Sql.Add
        ('select max(ITEM_5_1_5) cs,max(ITEM_5_1_6) js,max(ITEM_5_1_8) bc,max(ITEM_5_1_20) MaxTg,max(ITEM_5_1_21) Gz1,min(ITEM_5_1_17) Fz_b,max(ITEM_5_1_18) Fz_e,'
        + ' max(ifnull(ITEM_5_1_7,0)+ifnull(ITEM_5_1_9,0)+ifnull(ITEM_5_1_10,0)+ifnull(ITEM_5_1_11,0)*1/100*0.32+ifnull(ITEM_5_1_12,0)*5.2/100*0.32+ifnull(ITEM_5_1_13,0)+ifnull(ITEM_5_1_14,0)+ifnull(ITEM_5_1_15,0)+ifnull(ITEM_5_1_16,0)) '
        + ' YJL ,max(ifnull(ITEM_5_1_7,0)) ZLYJL from dm_ksmaterialrecord_5,xx_ks,xx_dd,cc_material '
        + ' where cc_material.ddid = xx_dd.ddid and dm_ksmaterialrecord_5.ksid = xx_ks.ksid and xx_ks.kh = xx_dd.kh and xx_ks.kz = xx_dd.ddkz and xx_ks.lb = xx_dd.ddlb '
        + ' and xx_ks.ml = xx_dd.ddml and xx_ks.ks = xx_dd.ddks and xx_ks.ys = xx_dd.ddys and xx_ks.ksbm = xx_dd.ksbm and cc_material.materialid in '
        + MaterialIdStr);
    end;
    if Fztypenum = 2 then
    begin
      DMUtilSelect.UniQuery1.Sql.Add
        ('select max(ITEM_5_2_5) cs,max(ITEM_5_2_6) js,max(ITEM_5_2_8) bc,max(ITEM_5_2_21) MaxTg,max(ITEM_5_2_22) Gz1,min(ITEM_5_2_18) Fz_b,max(ITEM_5_2_19) Fz_e,'
        + ' max(ifnull(ITEM_5_2_7,0)+ifnull(ITEM_5_2_9,0)+ifnull(ITEM_5_2_10,0)+ifnull(ITEM_5_2_11,0)*1/100*0.32+ifnull(ITEM_5_2_12,0)*1.7/100*0.32+ifnull(ITEM_5_2_13,0)*5.2/100*0.32+ifnull(ITEM_5_2_14,0)'
        + ' +ifnull(ITEM_5_2_15,0)+ifnull(ITEM_5_2_16,0)+ifnull(ITEM_5_2_17,0)) YJL,max(ifnull(ITEM_5_2_7,0)) ZLYJL from dm_ksmaterialrecord_5,xx_ks,xx_dd,cc_material '
        + ' where cc_material.ddid = xx_dd.ddid and dm_ksmaterialrecord_5.ksid = xx_ks.ksid and xx_ks.kh = xx_dd.kh and xx_ks.kz = xx_dd.ddkz and xx_ks.lb = xx_dd.ddlb '
        + ' and xx_ks.ml = xx_dd.ddml and xx_ks.ks = xx_dd.ddks and xx_ks.ys = xx_dd.ddys and xx_ks.ksbm = xx_dd.ksbm and cc_material.materialid in '
        + MaterialIdStr);
    end;
  end;

  DMUtilSelect.UniQuery1.ExecSQL;
  DMUtilSelect.UniQuery1.Active := true;

  if not DMUtilSelect.UniQuery1.Eof then
  begin
    if DMUtilSelect.UniQuery1.FieldByName('js').AsInteger > 0 then
    begin
      G_Js := IntToStr(DMUtilSelect.UniQuery1.FieldByName('js').AsInteger);
    end;

    if DMUtilSelect.UniQuery1.FieldByName('cs').AsInteger > 0 then
    begin
      G_Cs := IntToStr(DMUtilSelect.UniQuery1.FieldByName('cs').AsInteger);
    end;

    if DMUtilSelect.UniQuery1.FieldByName('bc').AsFloat > 0 then
    begin
      G_bc := DMUtilSelect.UniQuery1.FieldByName('bc').AsFloat / 100;
    end;

    if DMUtilSelect.UniQuery1.FieldByName('MaxTg').AsFloat > 0 then
    begin
      G_MaxTg := DMUtilSelect.UniQuery1.FieldByName('MaxTg').AsFloat;
    end;

    if DMUtilSelect.UniQuery1.FieldByName('Gz1').AsString <> '' then
    begin
      G_Gz1 := DMUtilSelect.UniQuery1.FieldByName('Gz1').AsString;
    end;

    if DMUtilSelect.UniQuery1.FieldByName('YJL').AsFloat > 0 then
    begin
      G_YJL := DMUtilSelect.UniQuery1.FieldByName('YJL').AsFloat;
    end;

    if DMUtilSelect.UniQuery1.FieldByName('Fz_b').AsFloat > 0 then
    begin
      G_Fz_B := floattostr(DMUtilSelect.UniQuery1.FieldByName('Fz_b').AsFloat);
    end;

    if DMUtilSelect.UniQuery1.FieldByName('Fz_e').AsFloat > 0 then
    begin
      G_Fz_E := floattostr(DMUtilSelect.UniQuery1.FieldByName('Fz_e').AsFloat);
    end;

    if DMUtilSelect.UniQuery1.FieldByName('ZLYJL').AsFloat >= 0 then
    begin
      G_ZLYJL := DMUtilSelect.UniQuery1.FieldByName('ZLYJL').AsFloat;
    end;

  end;

  DMUtilSelect.UniQuery1.Close;

  G_PdSuccessFlag := false;

  // Timer1.Enabled := true;

  // 当无主料时，进行直接存数据库的操作
  if G_ZLYJL > 0 then
  begin
    Px(G_Js, G_Cs, G_Fz_B, G_Fz_E, Data1StringList, Data2StringList,
      Data3StringList, DataStringList_Kc, DataStringList_All);
  end
  else
  begin
    self.FillGridEmptyNoFz;
  end;

  if Fztypenum = 1 then
  begin
    DMUtilSave.UniQuery1.Close;
    DMUtilSave.UniQuery1.Sql.Clear;
    DMUtilSave.UniQuery1.Sql.Add
      ('update cc_material set CALSTATE_A = :CALSTATE where MATERIALID in ' +
      MaterialIdStr);
    DMUtilSave.UniQuery1.ParamByName('CALSTATE').Value := '未审核';
    DMUtilSave.UniQuery1.ExecSQL;
    DMUtilSave.UniQuery1.Close;
  end;

  if Fztypenum = 2 then
  begin
    DMUtilSave.UniQuery1.Close;
    DMUtilSave.UniQuery1.Sql.Clear;
    DMUtilSave.UniQuery1.Sql.Add
      ('update cc_material set CALSTATE_B = :CALSTATE where MATERIALID in ' +
      MaterialIdStr);
    DMUtilSave.UniQuery1.ParamByName('CALSTATE').Value := '未审核';
    DMUtilSave.UniQuery1.ExecSQL;
    DMUtilSave.UniQuery1.Close;
  end;

  if Fztypenum = 3 then
  begin
    DMUtilSave.UniQuery1.Close;
    DMUtilSave.UniQuery1.Sql.Clear;
    DMUtilSave.UniQuery1.Sql.Add
      ('update cc_material set CALSTATE_C = :CALSTATE where MATERIALID in ' +
      MaterialIdStr);
    DMUtilSave.UniQuery1.ParamByName('CALSTATE').Value := '未审核';
    DMUtilSave.UniQuery1.ExecSQL;
    DMUtilSave.UniQuery1.Close;
  end;

  if Fztypenum = 4 then
  begin
    DMUtilSave.UniQuery1.Close;
    DMUtilSave.UniQuery1.Sql.Clear;
    DMUtilSave.UniQuery1.Sql.Add
      ('update cc_material set CALSTATE_D = :CALSTATE where MATERIALID in ' +
      MaterialIdStr);
    DMUtilSave.UniQuery1.ParamByName('CALSTATE').Value := '未审核';
    DMUtilSave.UniQuery1.ExecSQL;
    DMUtilSave.UniQuery1.Close;
  end;

  if Fztypenum = 5 then
  begin
    DMUtilSave.UniQuery1.Close;
    DMUtilSave.UniQuery1.Sql.Clear;
    DMUtilSave.UniQuery1.Sql.Add
      ('update cc_material set CALSTATE_E = :CALSTATE where MATERIALID in ' +
      MaterialIdStr);
    DMUtilSave.UniQuery1.ParamByName('CALSTATE').Value := '未审核';
    DMUtilSave.UniQuery1.ExecSQL;
    DMUtilSave.UniQuery1.Close;
  end;

  if Fztypenum = 6 then
  begin
    DMUtilSave.UniQuery1.Close;
    DMUtilSave.UniQuery1.Sql.Clear;
    DMUtilSave.UniQuery1.Sql.Add
      ('update cc_material set CALSTATE_F = :CALSTATE where MATERIALID in ' +
      MaterialIdStr);
    DMUtilSave.UniQuery1.ParamByName('CALSTATE').Value := '未审核';
    DMUtilSave.UniQuery1.ExecSQL;
    DMUtilSave.UniQuery1.Close;
  end;

end;

procedure TThreadPdCal.Px(js, cs, fz_b, fz_e: string;
  Data1StringList, Data2StringList, Data3StringList, DataStringList_Kc,
  DataStringList_All: TStringList);
var
  ListAll, ListCalAll: TList;
  List1, List2, List3, List4: TList;
  i, k, ii, o: Integer;
  StringList: TStringList;
  messagestr: wideString;
  secondcount: Integer;
  S: Array of Integer;
  Step1: Array of Integer;
  Step2: Array of Integer;
  Step3: Array of Integer;
  Step4S: Array of Integer;
  Step1Need: Boolean;
  Step2Need: Boolean;
  Step3Need: Boolean;
  Step1Bubble: Array of Integer;
  Step2Bubble: Array of Integer;
  Step3Bubble: Array of Integer;
  ListStep1, ListStep2, ListStep3: TList;
  ListStep1S, ListStep1D, ListStep2S, ListStep2D, ListStep3S, ListStep3D: TList;
  Step1S, Step1D, Step2S, Step2D, Step3S, Step3D: TStringList;
  PxList, PxListFive: TList;
  n: Integer;
  StringList_S, StringList_D1, StringList_D2, StringList_D3, StringList_D4,
    StringList_D5: TStringList;
  StepIsDoFlag: Boolean;
begin

//  //生成空数据
//  // xuxlempty
//  if true then
//  begin
//    FillGridEmpty();
//    exit;
//  end;

  if G_CalType = 2 then
  begin
    FillGridEmptyNoFz();
  end
  else
  begin
    if FmulFlag = false then
    begin
      if ((G_Cs <> '200') and (G_Cs <> '175') and (G_Cs <> '150') and
        (G_Cs <> '125') and (G_Cs <> '100') and (G_Cs <> '75') and
        (G_Cs <> '50') and (G_Cs <> '25')) then
      begin
        // if fz_b = '1' then
        // begin
        FillGridEmpty();
        // end;
      end
      else
      begin

        CengShu := TOptimizationHelper.GetCachedIntValue(G_Cs) div 25;

        FNeedFive := false;
        FNeedTen := false;
        ListAll := TList.Create;

        // 读取数据
        setlength(S, CMPZSUM);

        // 使用优化的批量转换，避免重复的StrToInt转换
        var
          OptimizedArray: TArray<Integer>;
          i: Integer;
        begin
          OptimizedArray := TOptimizationHelper.StringListToIntArray(Data1StringList, 0, CMPZSUM);
          for i := 0 to CMPZSUM - 1 do
          begin
            S[i] := OptimizedArray[i];
          end;
        end;

        // 订单数量大于12000件，不用排挡计算
        if (S[0] + S[1] + S[2] + S[3] + S[4] + S[5] + S[6] + S[7] + S[8] + S[9]
          + S[10] + S[11] + S[12]) > 7949 then
        begin
          FillGridEmpty();
        end
        else
        begin
          List1 := CalOnceSingle(1, S, 1);
          List2 := CalOnceSingle(2, S, 1);
          List3 := CalOnceSingle(3, S, 1);
          List4 := CalOnceSingle(4, S, 1);

          for i := 0 to List1.Count - 1 do
          begin
            StringList := TStringList(List1.Items[i]);
            ListAll.Add(StringList);
          end;

          for i := 0 to List2.Count - 1 do
          begin
            StringList := TStringList(List2.Items[i]);
            ListAll.Add(StringList);
          end;

          for i := 0 to List3.Count - 1 do
          begin
            StringList := TStringList(List3.Items[i]);
            ListAll.Add(StringList);
          end;

          for i := 0 to List4.Count - 1 do
          begin
            StringList := TStringList(List4.Items[i]);
            ListAll.Add(StringList);
          end;

          FillOnce(ListAll);

          if ListAll.Count = 0 then
          begin
            FNeedFive := true;

            List1 := CalOnceFiveSingle(1, S, 1);
            List2 := CalOnceFiveSingle(2, S, 1);
            List3 := CalOnceFiveSingle(3, S, 1);
            List4 := CalOnceFiveSingle(4, S, 1);

            for i := 0 to List1.Count - 1 do
            begin
              StringList := TStringList(List1.Items[i]);
              ListAll.Add(StringList);
            end;

            for i := 0 to List2.Count - 1 do
            begin
              StringList := TStringList(List2.Items[i]);
              ListAll.Add(StringList);
            end;

            for i := 0 to List3.Count - 1 do
            begin
              StringList := TStringList(List3.Items[i]);
              ListAll.Add(StringList);
            end;

            for i := 0 to List4.Count - 1 do
            begin
              StringList := TStringList(List4.Items[i]);
              ListAll.Add(StringList);
            end;

            FillOnceFive(ListAll);

            // 这里加入订单数控制，如小于4000，则不用启动10刀排档
            if (ListAll.Count = 0) and
              ((S[0] + S[1] + S[2] + S[3] + S[4] + S[5] + S[6] + S[7] + S[8] +
              S[9] + S[10] + S[11] + S[12]) > 4000) and (StrToInt(js) <= 20)
            then
            begin

              FNeedTen := true;

              List1 := CalOnceTenSingle(1, S);
              List2 := CalOnceTenSingle(2, S);
              List3 := CalOnceTenSingle(3, S);
              // List4 := CalOnceTenSingle(4, S);

              for i := 0 to List1.Count - 1 do
              begin
                StringList := TStringList(List1.Items[i]);
                ListAll.Add(StringList);
              end;

              for i := 0 to List2.Count - 1 do
              begin
                StringList := TStringList(List2.Items[i]);
                ListAll.Add(StringList);
              end;

              for i := 0 to List3.Count - 1 do
              begin
                StringList := TStringList(List3.Items[i]);
                ListAll.Add(StringList);
              end;

              FillOnceTen(ListAll);

              if ListAll.Count = 0 then
              begin
                // 没有结果，只记录合计内容，以及5条空白数据
                // 空白数据要根据订单总数/1200+2 来做预留位置
                // if fz_b = '1' then
                // begin
                FillGridEmpty();
                // end;

              end;
            end
            else
            begin
              if (ListAll.Count = 0) then
              begin
                FillGridEmpty();
              end;
            end;

          end;

        end;

      end;

    end;

  end;

end;







{ 性能测试方法 - 测试字符串缓存和快速排序的性能提升 }
procedure TThreadPdCal.TestOptimizationPerformance;
var
  TestData: TStringList;
  TestArray: array of Integer;
  StartTime, EndTime: TDateTime;
  OriginalTime, OptimizedTime: Double;
  i: Integer;
  Sum1, Sum2: Integer;
begin
  TestData := TStringList.Create;
  try
    // 创建测试数据
    for i := 0 to 999 do
    begin
      TestData.Add(IntToStr(Random(1000)));
    end;

    // 测试字符串转换性能
    StartTime := Now;
    Sum1 := 0;
    for i := 0 to 99 do // 重复100次
    begin
      Sum1 := Sum1 + StrToInt(TestData.Strings[i mod TestData.Count]);
    end;
    EndTime := Now;
    OriginalTime := (EndTime - StartTime) * 24 * 60 * 60 * 1000;

    StartTime := Now;
    Sum2 := 0;
    for i := 0 to 99 do // 重复100次
    begin
      Sum2 := Sum2 + TOptimizationHelper.GetCachedIntValue(TestData.Strings[i mod TestData.Count]);
    end;
    EndTime := Now;
    OptimizedTime := (EndTime - StartTime) * 24 * 60 * 60 * 1000;

    // 测试排序性能
    SetLength(TestArray, 1000);
    for i := 0 to 999 do
    begin
      TestArray[i] := Random(10000);
    end;

    StartTime := Now;
    BubbleSort(TestArray); // 现在内部使用快速排序
    EndTime := Now;

    // 输出结果到文件
    var
      ResultList: TStringList;
    begin
      ResultList := TStringList.Create;
      try
      ResultList.Add('=== 性能优化测试结果 ===');
      ResultList.Add('字符串转换测试:');
      ResultList.Add('  原始方法: ' + FormatFloat('0.00', OriginalTime) + ' 毫秒');
      ResultList.Add('  优化方法: ' + FormatFloat('0.00', OptimizedTime) + ' 毫秒');
      if OptimizedTime > 0 then
        ResultList.Add('  性能提升: ' + FormatFloat('0.00', (OriginalTime - OptimizedTime) / OriginalTime * 100) + '%');

      ResultList.Add('');
      ResultList.Add('排序算法测试:');
      ResultList.Add('  快速排序时间: ' + FormatFloat('0.00', (EndTime - StartTime) * 24 * 60 * 60 * 1000) + ' 毫秒');

      ResultList.Add('');
      ResultList.Add('缓存统计:');
      ResultList.Add('  缓存大小: ' + IntToStr(TOptimizationHelper.FStringCache.Count) + ' 项');

      ResultList.SaveToFile('optimization_test_results.txt');
      finally
        ResultList.Free;
      end;
    end;

  finally
    TestData.Free;
    SetLength(TestArray, 0);
  end;
end;

end.
