program TestOptimization;

{$APPTYPE CONSOLE}

uses
  SysUtils, Classes, ThreadPdCal;

var
  ThreadPdCal: TThreadPdCal;
  TestData: TStringList;
  i: Integer;
  StartTime, EndTime: TDateTime;
  TestTime: Double;

begin
  WriteLn('=== 生产排档计算系统性能优化测试 ===');
  WriteLn;

  ThreadPdCal := TThreadPdCal.Create;
  TestData := TStringList.Create;
  try
    // 创建测试数据
    WriteLn('创建测试数据...');
    for i := 0 to 12 do
    begin
      TestData.Add(IntToStr(Random(1000) + 1));
    end;

    // 测试字符串缓存功能
    WriteLn('测试字符串缓存功能...');
    StartTime := Now;
    for i := 0 to 999 do
    begin
      TOptimizationHelper.GetCachedIntValue(TestData.Strings[i mod TestData.Count]);
    end;
    EndTime := Now;
    TestTime := (EndTime - StartTime) * 24 * 60 * 60 * 1000;
    WriteLn('缓存转换1000次用时: ', FormatFloat('0.00', TestTime), ' 毫秒');

    // 测试批量转换功能
    WriteLn('测试批量转换功能...');
    StartTime := Now;
    for i := 0 to 99 do
    begin
      TOptimizationHelper.StringListToIntArray(TestData, 0, TestData.Count);
    end;
    EndTime := Now;
    TestTime := (EndTime - StartTime) * 24 * 60 * 60 * 1000;
    WriteLn('批量转换100次用时: ', FormatFloat('0.00', TestTime), ' 毫秒');

    // 测试批量求和功能
    WriteLn('测试批量求和功能...');
    StartTime := Now;
    for i := 0 to 99 do
    begin
      TOptimizationHelper.CalculateStringListSum(TestData, 0, TestData.Count);
    end;
    EndTime := Now;
    TestTime := (EndTime - StartTime) * 24 * 60 * 60 * 1000;
    WriteLn('批量求和100次用时: ', FormatFloat('0.00', TestTime), ' 毫秒');

    // 运行完整性能测试
    WriteLn;
    WriteLn('运行完整性能测试...');
    ThreadPdCal.TestOptimizationPerformance;
    WriteLn('测试结果已保存到 optimization_test_results.txt');

    WriteLn;
    WriteLn('缓存统计:');
    WriteLn('  缓存项数: ', TOptimizationHelper.FStringCache.Count);

  finally
    TestData.Free;
    ThreadPdCal.Free;
  end;

  WriteLn;
  WriteLn('测试完成！按回车键退出...');
  ReadLn;
end.
